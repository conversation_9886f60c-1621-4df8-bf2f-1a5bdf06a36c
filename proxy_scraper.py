#!/usr/bin/env python3
"""
代理版LeBonCoin房产爬虫
支持使用代理服务器绕过IP封禁
"""

import asyncio
import csv
import json
import re
import random
from datetime import datetime
from playwright.async_api import async_playwright
from playwright_stealth import stealth_async

class ProxyScraper:
    def __init__(self, proxy_config=None):
        self.browser = None
        self.page = None
        self.properties = []
        self.proxy_config = proxy_config
    
    async def init_browser(self):
        """初始化浏览器（支持代理）"""
        print("🚀 正在启动浏览器...")
        if self.proxy_config:
            print(f"🌐 使用代理: {self.proxy_config['server']}")
        
        playwright = await async_playwright().start()
        
        # 浏览器启动参数
        browser_args = [
            '--no-sandbox',
            '--disable-dev-shm-usage',
        ]
        
        self.browser = await playwright.chromium.launch(
            headless=False,
            args=browser_args
        )
        
        # 创建上下文（包含代理配置）
        context_options = {
            'viewport': {'width': 1366, 'height': 768},
            'user_agent': self.get_random_user_agent(),
            'locale': 'fr-FR',
            'timezone_id': 'Europe/Paris',
        }
        
        # 添加代理配置
        if self.proxy_config:
            context_options['proxy'] = self.proxy_config
        
        context = await self.browser.new_context(**context_options)
        self.page = await context.new_page()
        
        # 应用stealth（轻量级）
        await stealth_async(self.page)
        
        print("✅ 浏览器启动成功")
    
    def get_random_user_agent(self):
        """获取随机User-Agent"""
        user_agents = [
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
        ]
        return random.choice(user_agents)
    
    async def test_ip_status(self):
        """测试当前IP状态"""
        print("🔍 测试IP状态...")
        
        try:
            await self.page.goto("https://httpbin.org/ip", timeout=15000)
            content = await self.page.content()
            
            # 提取IP信息
            if '"origin"' in content:
                import json
                ip_data = json.loads(await self.page.inner_text('pre'))
                current_ip = ip_data.get('origin', 'Unknown')
                print(f"📍 当前IP: {current_ip}")
            
            # 测试LeBonCoin访问
            print("🌐 测试LeBonCoin访问...")
            await self.page.goto("https://www.leboncoin.fr", timeout=30000)
            await asyncio.sleep(3)
            
            content = await self.page.content()
            title = await self.page.title()
            
            if "pourquoi ce blocage" in content.lower() or "blocage" in content.lower():
                print("❌ IP仍被封禁")
                return False
            elif "please enable js" in content.lower():
                print("🔒 遇到验证页面（IP可用）")
                return True
            else:
                print("✅ IP状态正常")
                return True
                
        except Exception as e:
            print(f"❌ IP测试失败: {e}")
            return False
    
    async def wait_for_unblock(self, max_wait_minutes=30):
        """等待IP解封"""
        print(f"⏳ 等待IP解封（最多{max_wait_minutes}分钟）...")
        
        for minute in range(max_wait_minutes):
            print(f"⏰ 等待中... {minute+1}/{max_wait_minutes} 分钟")
            await asyncio.sleep(60)  # 等待1分钟
            
            # 测试是否解封
            if await self.test_ip_status():
                print("🎉 IP已解封！")
                return True
        
        print("⏰ 等待超时，IP仍被封禁")
        return False
    
    async def navigate_carefully(self, url):
        """谨慎导航（模拟人类行为）"""
        print(f"🌐 谨慎访问: {url}")
        
        try:
            # 随机延迟
            delay = random.uniform(3, 8)
            print(f"⏳ 随机延迟 {delay:.1f} 秒...")
            await asyncio.sleep(delay)
            
            # 访问页面
            await self.page.goto(url, wait_until="domcontentloaded", timeout=30000)
            
            # 模拟人类行为
            await self.simulate_human_behavior()
            
            # 检查页面状态
            content = await self.page.content()
            title = await self.page.title()
            
            print(f"📄 页面标题: {title}")
            
            if "pourquoi ce blocage" in content.lower():
                print("❌ 再次被封禁")
                return False
            elif "please enable js" in content.lower() or "verification" in content.lower():
                print("🔒 遇到验证页面")
                await self.handle_verification()
                return True
            else:
                print("✅ 访问成功")
                return True
                
        except Exception as e:
            print(f"❌ 访问失败: {e}")
            return False
    
    async def simulate_human_behavior(self):
        """模拟人类行为"""
        try:
            # 随机鼠标移动
            for _ in range(random.randint(2, 4)):
                x = random.randint(100, 800)
                y = random.randint(100, 600)
                await self.page.mouse.move(x, y)
                await asyncio.sleep(random.uniform(0.5, 1.5))
            
            # 随机滚动
            for _ in range(random.randint(1, 3)):
                scroll_amount = random.randint(200, 500)
                await self.page.evaluate(f"window.scrollBy(0, {scroll_amount})")
                await asyncio.sleep(random.uniform(1, 3))
            
            # 随机停顿
            await asyncio.sleep(random.uniform(2, 5))
            
        except Exception as e:
            print(f"⚠️  行为模拟失败: {e}")
    
    async def handle_verification(self):
        """处理验证页面"""
        print("🔒 检测到验证页面，等待手动处理...")
        
        await self.page.screenshot(path="verification_with_proxy.png")
        
        print("\n" + "="*50)
        print("请在浏览器中完成验证：")
        print("1. 完成滑块验证")
        print("2. 点击验证按钮")
        print("3. 等待页面加载")
        print("="*50)
        
        input("完成验证后按 Enter 继续...")
        
        # 验证完成后等待
        await asyncio.sleep(3)
    
    async def extract_properties_safe(self):
        """安全的房产信息提取"""
        print("🏠 开始安全提取房产信息...")
        
        # 等待页面稳定
        await asyncio.sleep(random.uniform(3, 6))
        
        # 保存页面状态
        await self.page.screenshot(path="extraction_with_proxy.png")
        
        try:
            # 查找价格元素
            price_elements = await self.page.query_selector_all("*:has-text('€')")
            print(f"💰 找到 {len(price_elements)} 个包含价格的元素")
            
            if len(price_elements) < 3:
                print("⚠️  价格元素太少，可能需要手动操作")
                manual_help = input("是否需要手动滚动页面加载更多内容？(y/n): ")
                if manual_help.lower() == 'y':
                    input("请手动滚动页面，完成后按 Enter 继续...")
                    price_elements = await self.page.query_selector_all("*:has-text('€')")
                    print(f"💰 重新检查，找到 {len(price_elements)} 个价格元素")
            
            # 谨慎处理每个元素
            for i, element in enumerate(price_elements[:15]):  # 限制数量避免过度请求
                try:
                    property_data = await self.extract_single_property_safe(element, i)
                    if property_data:
                        self.properties.append(property_data)
                        print(f"  ✅ 房产 {len(self.properties)}: {property_data.get('price', 'Unknown')}€")
                    
                    # 每处理几个元素就休息一下
                    if (i + 1) % 5 == 0:
                        delay = random.uniform(2, 4)
                        print(f"  ⏳ 休息 {delay:.1f} 秒...")
                        await asyncio.sleep(delay)
                        
                except Exception as e:
                    print(f"  ❌ 处理元素 {i+1} 失败: {e}")
                    continue
            
            print(f"📊 安全提取完成，共获得 {len(self.properties)} 个房产")
            
        except Exception as e:
            print(f"❌ 提取过程失败: {e}")
    
    async def extract_single_property_safe(self, price_element, index):
        """安全提取单个房产"""
        try:
            # 获取父容器
            parent = price_element
            for _ in range(3):
                try:
                    parent_candidate = await parent.query_selector('xpath=..')
                    if parent_candidate:
                        parent = parent_candidate
                    else:
                        break
                except:
                    break
            
            full_text = await parent.inner_text()
            
            if len(full_text) < 20:
                return None
            
            property_data = {
                'index': index + 1,
                'extracted_at': datetime.now().isoformat()
            }
            
            # 提取价格
            price = self.extract_price_from_text(full_text)
            if not price:
                return None
            
            property_data['price'] = price
            
            # 提取其他信息
            area = self.extract_area_from_text(full_text)
            if area:
                property_data['area'] = area
                property_data['price_per_sqm'] = round(price / area, 2)
            
            rooms = self.extract_rooms_from_text(full_text)
            if rooms:
                property_data['rooms'] = rooms
            
            location = self.extract_location_from_text(full_text)
            if location:
                property_data['location'] = location
            
            # 提取标题
            lines = [line.strip() for line in full_text.split('\n') if line.strip()]
            if lines:
                property_data['title'] = lines[0][:100]
            
            return property_data
            
        except Exception as e:
            return None
    
    def extract_price_from_text(self, text):
        """从文本中提取价格"""
        patterns = [
            r'(\d{1,3}(?:\s\d{3})*)\s*€',
            r'(\d+(?:\.\d{3})*)\s*€',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                try:
                    price_str = match.replace(' ', '').replace('.', '')
                    price = int(price_str)
                    if 10000 <= price <= 10000000:
                        return price
                except:
                    continue
        return None
    
    def extract_area_from_text(self, text):
        """从文本中提取面积"""
        pattern = r'(\d+(?:[.,]\d+)?)\s*m[²2]'
        matches = re.findall(pattern, text)
        for match in matches:
            try:
                area = float(match.replace(',', '.'))
                if 10 <= area <= 1000:
                    return area
            except:
                continue
        return None
    
    def extract_rooms_from_text(self, text):
        """从文本中提取房间数"""
        patterns = [r'(\d+)\s*pièces?', r'T(\d+)']
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                try:
                    rooms = int(match)
                    if 1 <= rooms <= 20:
                        return rooms
                except:
                    continue
        return None
    
    def extract_location_from_text(self, text):
        """从文本中提取位置"""
        pattern = r'(\d{5})\s+([A-Za-zÀ-ÿ\s-]+)'
        matches = re.findall(pattern, text)
        if matches:
            return ' '.join(matches[0]).strip()
        return None
    
    async def save_results(self):
        """保存结果"""
        if not self.properties:
            print("❌ 没有数据可保存")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_filename = f"leboncoin_proxy_{timestamp}.csv"
        
        fieldnames = set()
        for prop in self.properties:
            fieldnames.update(prop.keys())
        fieldnames = sorted(list(fieldnames))
        
        with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(self.properties)
        
        print(f"✅ 数据已保存: {csv_filename}")
        print(f"📊 总共获取 {len(self.properties)} 个房产信息")
    
    async def close(self):
        """关闭浏览器"""
        if self.browser:
            await self.browser.close()

async def main():
    """主函数"""
    print("🏠 代理版LeBonCoin房产爬虫")
    print("专门解决IP封禁问题")
    print("="*50)
    
    # 代理配置（可选）
    proxy_config = None
    
    use_proxy = input("是否使用代理服务器？(y/n): ")
    if use_proxy.lower() == 'y':
        proxy_server = input("请输入代理服务器地址 (例如: http://proxy.example.com:8080): ")
        proxy_username = input("代理用户名 (可选，直接回车跳过): ")
        proxy_password = input("代理密码 (可选，直接回车跳过): ")
        
        proxy_config = {"server": proxy_server}
        if proxy_username:
            proxy_config["username"] = proxy_username
        if proxy_password:
            proxy_config["password"] = proxy_password
    
    scraper = ProxyScraper(proxy_config)
    
    try:
        await scraper.init_browser()
        
        # 测试IP状态
        if not await scraper.test_ip_status():
            print("🚫 IP被封禁，选择处理方式：")
            print("1. 等待自动解封")
            print("2. 使用代理服务器")
            print("3. 退出程序")
            
            choice = input("请选择 (1/2/3): ")
            
            if choice == "1":
                if not await scraper.wait_for_unblock():
                    print("❌ 等待超时，请稍后重试")
                    return
            elif choice == "2":
                print("请重新运行程序并配置代理")
                return
            else:
                return
        
        # 访问搜索页面
        search_url = "https://www.leboncoin.fr/recherche?category=9&text=NOT%20construire&locations=Palaiseau_91120__48.71303_2.24628_4002&immo_sell_type=old&real_estate_type=1,2&owner_type=all"
        
        if await scraper.navigate_carefully(search_url):
            await scraper.extract_properties_safe()
            await scraper.save_results()
        else:
            print("❌ 无法访问搜索页面")
    
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
    
    finally:
        await scraper.close()

if __name__ == "__main__":
    asyncio.run(main())
