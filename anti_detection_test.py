#!/usr/bin/env python3
"""
反检测测试脚本
专门测试和解决"Please enable J<PERSON> and disable any ad blocker"问题
"""

import asyncio
from playwright.async_api import async_playwright
from playwright_stealth import stealth_async

async def test_anti_detection():
    """测试反检测能力"""
    print("🛡️ 反检测测试开始")
    print("="*50)
    
    playwright = await async_playwright().start()
    
    # 配置1：基础配置
    print("\n📋 测试1: 基础浏览器配置")
    browser1 = await playwright.chromium.launch(headless=False)
    page1 = await browser1.new_page()
    
    try:
        await page1.goto("https://www.leboncoin.fr", timeout=30000)
        await asyncio.sleep(5)
        title1 = await page1.title()
        content1 = await page1.content()
        
        if "please enable js" in content1.lower():
            print("❌ 基础配置被检测")
        else:
            print("✅ 基础配置通过")
        
        await page1.screenshot(path="test1_basic.png")
    except Exception as e:
        print(f"❌ 基础配置失败: {e}")
    
    await browser1.close()
    
    # 配置2：Stealth配置
    print("\n📋 测试2: Stealth插件配置")
    browser2 = await playwright.chromium.launch(
        headless=False,
        args=['--no-sandbox', '--disable-dev-shm-usage']
    )
    context2 = await browser2.new_context(
        user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    )
    page2 = await context2.new_page()
    await stealth_async(page2)
    
    try:
        await page2.goto("https://www.leboncoin.fr", timeout=30000)
        await asyncio.sleep(5)
        title2 = await page2.title()
        content2 = await page2.content()
        
        if "please enable js" in content2.lower():
            print("❌ Stealth配置被检测")
        else:
            print("✅ Stealth配置通过")
        
        await page2.screenshot(path="test2_stealth.png")
    except Exception as e:
        print(f"❌ Stealth配置失败: {e}")
    
    await browser2.close()
    
    # 配置3：增强配置
    print("\n📋 测试3: 增强反检测配置")
    browser3 = await playwright.chromium.launch(
        headless=False,
        args=[
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-blink-features=AutomationControlled',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--no-first-run',
            '--disable-default-apps',
            '--disable-popup-blocking',
            '--disable-translate',
            '--disable-extensions',
        ]
    )
    
    context3 = await browser3.new_context(
        viewport={'width': 1366, 'height': 768},
        user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        locale='fr-FR',
        timezone_id='Europe/Paris',
        extra_http_headers={
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
            'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
    )
    
    page3 = await context3.new_page()
    await stealth_async(page3)
    
    # 注入反检测脚本
    await page3.add_init_script("""
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        window.chrome = {
            runtime: {},
            loadTimes: function() {},
            csi: function() {},
            app: {}
        };
        
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5],
        });
        
        Object.defineProperty(navigator, 'languages', {
            get: () => ['fr-FR', 'fr', 'en'],
        });
    """)
    
    try:
        await page3.goto("https://www.leboncoin.fr", timeout=30000)
        await asyncio.sleep(8)  # 更长等待时间
        
        # 模拟人类行为
        await page3.mouse.move(100, 100)
        await page3.mouse.move(200, 200)
        await asyncio.sleep(2)
        
        title3 = await page3.title()
        content3 = await page3.content()
        
        if "please enable js" in content3.lower():
            print("❌ 增强配置仍被检测")
            print("🔍 尝试等待更长时间...")
            await asyncio.sleep(15)
            
            # 再次检查
            content3_retry = await page3.content()
            if "please enable js" in content3_retry.lower():
                print("❌ 延长等待后仍被检测")
            else:
                print("✅ 延长等待后通过检测")
        else:
            print("✅ 增强配置通过")
        
        await page3.screenshot(path="test3_enhanced.png")
        
        # 保存页面HTML用于分析
        with open("test3_page.html", "w", encoding="utf-8") as f:
            f.write(content3)
        
    except Exception as e:
        print(f"❌ 增强配置失败: {e}")
    
    await browser3.close()
    
    # 配置4：最大化反检测
    print("\n📋 测试4: 最大化反检测配置")
    browser4 = await playwright.chromium.launch(
        headless=False,
        args=[
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-blink-features=AutomationControlled',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-extensions-except',
            '--disable-plugins-discovery',
            '--no-first-run',
            '--no-default-browser-check',
            '--disable-default-apps',
            '--disable-popup-blocking',
            '--disable-translate',
            '--disable-background-timer-throttling',
            '--disable-renderer-backgrounding',
            '--disable-device-discovery-notifications',
            '--disable-ipc-flooding-protection',
            '--enable-features=NetworkService,NetworkServiceLogging',
            '--force-color-profile=srgb',
            '--disable-background-networking',
        ]
    )
    
    context4 = await browser4.new_context(
        viewport={'width': 1366, 'height': 768},
        user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        locale='fr-FR',
        timezone_id='Europe/Paris',
        geolocation={'latitude': 48.8566, 'longitude': 2.3522},
        permissions=['geolocation'],
        extra_http_headers={
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
        }
    )
    
    page4 = await context4.new_page()
    await stealth_async(page4)
    
    # 注入最强反检测脚本
    await page4.add_init_script("""
        // 移除webdriver属性
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        // 伪造chrome对象
        window.chrome = {
            runtime: {},
            loadTimes: function() {},
            csi: function() {},
            app: {}
        };
        
        // 伪造插件
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5],
        });
        
        // 伪造语言
        Object.defineProperty(navigator, 'languages', {
            get: () => ['fr-FR', 'fr', 'en'],
        });
        
        // 移除自动化痕迹
        const originalQuery = window.document.querySelector;
        window.document.querySelector = function(selector) {
            if (selector.includes('webdriver') || selector.includes('automation')) {
                return null;
            }
            return originalQuery.call(this, selector);
        };
        
        // 伪造屏幕信息
        Object.defineProperty(screen, 'colorDepth', {
            get: () => 24,
        });
        
        Object.defineProperty(screen, 'pixelDepth', {
            get: () => 24,
        });
        
        // 伪造时区
        Date.prototype.getTimezoneOffset = function() {
            return -60; // 巴黎时区
        };
        
        // 伪造canvas指纹
        const getContext = HTMLCanvasElement.prototype.getContext;
        HTMLCanvasElement.prototype.getContext = function(type) {
            if (type === '2d') {
                const context = getContext.call(this, type);
                const originalFillText = context.fillText;
                context.fillText = function() {
                    return originalFillText.apply(this, arguments);
                };
                return context;
            }
            return getContext.call(this, type);
        };
    """)
    
    try:
        print("🌐 正在访问LeBonCoin...")
        await page4.goto("https://www.leboncoin.fr", timeout=60000)
        
        print("⏳ 等待页面加载...")
        await asyncio.sleep(10)
        
        # 更复杂的人类行为模拟
        print("🤖 模拟人类行为...")
        await page4.mouse.move(300, 200)
        await asyncio.sleep(1)
        await page4.mouse.move(500, 400)
        await asyncio.sleep(1)
        await page4.evaluate("window.scrollBy(0, 300)")
        await asyncio.sleep(2)
        await page4.keyboard.press('Tab')
        await asyncio.sleep(1)
        
        title4 = await page4.title()
        content4 = await page4.content()
        current_url = page4.url
        
        print(f"📄 页面标题: {title4}")
        print(f"🔗 当前URL: {current_url}")
        
        if "please enable js" in content4.lower() or "disable any ad blocker" in content4.lower():
            print("❌ 最大化配置仍被检测")
            print("🔍 检查页面内容...")
            
            # 查找可能的验证元素
            verification_elements = await page4.query_selector_all('input[type="checkbox"], button, [class*="challenge"]')
            print(f"🔘 找到 {len(verification_elements)} 个可能的验证元素")
            
            # 尝试点击验证
            for element in verification_elements[:3]:
                try:
                    await element.click()
                    await asyncio.sleep(3)
                    print("🔘 已点击验证元素")
                except:
                    continue
            
            # 再次等待
            await asyncio.sleep(15)
            content4_final = await page4.content()
            
            if "please enable js" in content4_final.lower():
                print("❌ 最终仍被检测，可能需要手动验证")
            else:
                print("✅ 验证后通过检测")
        else:
            print("✅ 最大化配置成功通过")
        
        await page4.screenshot(path="test4_maximum.png")
        
        # 保存最终页面
        with open("test4_final.html", "w", encoding="utf-8") as f:
            f.write(content4)
        
        print("📸 已保存截图和页面HTML")
        
    except Exception as e:
        print(f"❌ 最大化配置失败: {e}")
    
    # 保持浏览器打开以便手动检查
    print("\n🔍 浏览器将保持打开30秒，请手动检查页面...")
    await asyncio.sleep(30)
    
    await browser4.close()
    await playwright.stop()
    
    print("\n" + "="*50)
    print("📊 测试完成！")
    print("请查看生成的截图和HTML文件：")
    print("- test1_basic.png")
    print("- test2_stealth.png") 
    print("- test3_enhanced.png")
    print("- test4_maximum.png")
    print("- test3_page.html")
    print("- test4_final.html")

if __name__ == "__main__":
    asyncio.run(test_anti_detection())
