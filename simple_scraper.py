#!/usr/bin/env python3
"""
简化版LeBonCoin房产爬虫
专门针对实际网站结构优化
"""

import asyncio
import csv
import json
import re
from datetime import datetime
from playwright.async_api import async_playwright
from playwright_stealth import stealth_async
from fake_useragent import UserAgent

class SimpleLeBonCoinScraper:
    def __init__(self):
        self.browser = None
        self.page = None
        self.properties = []
        self.ua = UserAgent()
    
    async def init_browser(self):
        """初始化浏览器"""
        print("🚀 正在启动浏览器...")
        
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=False,  # 设为True可以隐藏浏览器
            args=[
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
            ]
        )
        
        context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent=self.ua.chrome,
            locale='fr-FR',
            timezone_id='Europe/Paris'
        )
        
        self.page = await context.new_page()
        await stealth_async(self.page)
        
        # 设置额外的反检测措施
        await self.page.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
        """)
        
        print("✅ 浏览器启动成功")
    
    async def navigate_to_search(self, search_url):
        """导航到搜索页面"""
        print(f"🌐 正在访问: {search_url}")
        
        try:
            await self.page.goto(search_url, wait_until="networkidle", timeout=30000)
            await asyncio.sleep(3)  # 等待页面稳定
            
            title = await self.page.title()
            print(f"📄 页面标题: {title}")
            
            # 检查是否需要接受cookies
            await self.handle_cookies()
            
            return True
        except Exception as e:
            print(f"❌ 访问失败: {e}")
            return False
    
    async def handle_cookies(self):
        """处理cookie弹窗"""
        try:
            # 常见的cookie接受按钮选择器
            cookie_selectors = [
                "button:has-text('Accepter')",
                "button:has-text('Accept')",
                "button:has-text('Tout accepter')",
                "[data-test-id*='accept']",
                ".cookie-accept",
                "#didomi-notice-agree-button"
            ]
            
            for selector in cookie_selectors:
                try:
                    button = await self.page.query_selector(selector)
                    if button:
                        await button.click()
                        print("✅ 已接受cookies")
                        await asyncio.sleep(2)
                        return
                except:
                    continue
        except:
            pass
    
    async def extract_properties(self):
        """提取房产信息"""
        print("🏠 正在提取房产信息...")
        
        # 等待页面加载
        await asyncio.sleep(5)
        
        # 尝试多种可能的选择器来找到房产列表
        property_selectors = [
            "[data-test-id*='ad']",
            "article",
            ".ad-card",
            ".listing-item",
            "[class*='ad-']",
            ".search-result"
        ]
        
        properties_found = False
        
        for selector in property_selectors:
            try:
                elements = await self.page.query_selector_all(selector)
                if len(elements) >= 5:  # 至少找到5个元素才认为是有效的
                    print(f"✅ 使用选择器 '{selector}' 找到 {len(elements)} 个房产")
                    await self.extract_from_elements(elements)
                    properties_found = True
                    break
            except Exception as e:
                continue
        
        if not properties_found:
            print("❌ 未找到房产列表，尝试通用方法...")
            await self.extract_with_generic_method()
    
    async def extract_from_elements(self, elements):
        """从找到的元素中提取信息"""
        for i, element in enumerate(elements[:20]):  # 限制处理前20个
            try:
                property_data = await self.extract_single_property(element, i)
                if property_data:
                    self.properties.append(property_data)
                    print(f"  ✅ 提取房产 {i+1}: {property_data.get('title', 'Unknown')[:50]}...")
            except Exception as e:
                print(f"  ❌ 提取房产 {i+1} 失败: {e}")
                continue
    
    async def extract_single_property(self, element, index):
        """提取单个房产信息"""
        property_data = {
            'index': index + 1,
            'extracted_at': datetime.now().isoformat()
        }
        
        # 提取链接
        link = await self.find_link_in_element(element)
        if link:
            property_data['url'] = link
        
        # 提取标题
        title = await self.find_title_in_element(element)
        if title:
            property_data['title'] = title
        
        # 提取价格
        price = await self.find_price_in_element(element)
        if price:
            property_data['price'] = price
        
        # 提取图片
        image = await self.find_image_in_element(element)
        if image:
            property_data['image'] = image
        
        # 提取位置
        location = await self.find_location_in_element(element)
        if location:
            property_data['location'] = location
        
        # 提取其他属性（面积、房间数等）
        attributes = await self.find_attributes_in_element(element)
        property_data.update(attributes)
        
        return property_data if property_data.get('title') or property_data.get('price') else None
    
    async def find_link_in_element(self, element):
        """在元素中查找链接"""
        link_selectors = ["a[href*='/ad/']", "a[href*='/annonce/']", "a"]
        
        for selector in link_selectors:
            try:
                link_element = await element.query_selector(selector)
                if link_element:
                    href = await link_element.get_attribute('href')
                    if href:
                        if href.startswith('/'):
                            href = 'https://www.leboncoin.fr' + href
                        return href
            except:
                continue
        return None
    
    async def find_title_in_element(self, element):
        """在元素中查找标题"""
        title_selectors = ["h1", "h2", "h3", "h4", "[class*='title']", "[data-test-id*='title']"]
        
        for selector in title_selectors:
            try:
                title_element = await element.query_selector(selector)
                if title_element:
                    title = await title_element.inner_text()
                    if title and len(title.strip()) > 5:
                        return title.strip()
            except:
                continue
        return None
    
    async def find_price_in_element(self, element):
        """在元素中查找价格"""
        try:
            text_content = await element.inner_text()
            # 使用正则表达式查找价格
            price_patterns = [
                r'(\d{1,3}(?:\s?\d{3})*)\s*€',
                r'(\d+(?:\.\d{3})*)\s*€',
                r'€\s*(\d{1,3}(?:\s?\d{3})*)',
            ]
            
            for pattern in price_patterns:
                matches = re.findall(pattern, text_content)
                if matches:
                    price_str = matches[0].replace(' ', '').replace('.', '')
                    try:
                        price = int(price_str)
                        if 10000 <= price <= 10000000:  # 合理的房价范围
                            return price
                    except:
                        continue
        except:
            pass
        return None
    
    async def find_image_in_element(self, element):
        """在元素中查找图片"""
        try:
            img_element = await element.query_selector("img")
            if img_element:
                src = await img_element.get_attribute('src')
                if src and ('http' in src or src.startswith('/')):
                    if src.startswith('/'):
                        src = 'https://www.leboncoin.fr' + src
                    return src
        except:
            pass
        return None
    
    async def find_location_in_element(self, element):
        """在元素中查找位置信息"""
        try:
            text_content = await element.inner_text()
            # 查找法国城市名称模式
            location_patterns = [
                r'(\d{5})\s+([A-Za-zÀ-ÿ\s-]+)',  # 邮编 + 城市名
                r'([A-Za-zÀ-ÿ\s-]+)\s+\((\d{2,3})\)',  # 城市名 + 省份代码
            ]
            
            for pattern in location_patterns:
                matches = re.findall(pattern, text_content)
                if matches:
                    return ' '.join(matches[0]).strip()
        except:
            pass
        return None
    
    async def find_attributes_in_element(self, element):
        """在元素中查找房产属性"""
        attributes = {}
        
        try:
            text_content = await element.inner_text()
            
            # 查找面积
            area_patterns = [
                r'(\d+(?:[.,]\d+)?)\s*m[²2]',
                r'(\d+)\s*m[²2]'
            ]
            
            for pattern in area_patterns:
                matches = re.findall(pattern, text_content)
                if matches:
                    try:
                        area = float(matches[0].replace(',', '.'))
                        attributes['area'] = area
                        break
                    except:
                        continue
            
            # 查找房间数
            room_patterns = [
                r'(\d+)\s*pièces?',
                r'(\d+)\s*p\.',
                r'T(\d+)',
            ]
            
            for pattern in room_patterns:
                matches = re.findall(pattern, text_content, re.IGNORECASE)
                if matches:
                    try:
                        rooms = int(matches[0])
                        attributes['rooms'] = rooms
                        break
                    except:
                        continue
            
            # 计算平米价格
            if 'area' in attributes and attributes.get('area', 0) > 0:
                price = self.properties[-1].get('price') if self.properties else None
                if not price:
                    # 尝试从当前元素获取价格
                    price = await self.find_price_in_element(element)
                
                if price and price > 0:
                    attributes['price_per_sqm'] = round(price / attributes['area'], 2)
            
        except:
            pass
        
        return attributes
    
    async def extract_with_generic_method(self):
        """使用通用方法提取信息"""
        print("🔍 使用通用方法分析页面...")
        
        try:
            # 获取页面所有文本内容
            content = await self.page.content()
            
            # 查找所有包含价格的元素
            price_elements = await self.page.query_selector_all("*:has-text('€')")
            
            print(f"找到 {len(price_elements)} 个包含价格的元素")
            
            for i, element in enumerate(price_elements[:10]):  # 处理前10个
                try:
                    # 获取父元素，可能包含完整的房产信息
                    parent = await element.query_selector('xpath=..')
                    if parent:
                        property_data = await self.extract_single_property(parent, i)
                        if property_data:
                            self.properties.append(property_data)
                except:
                    continue
                    
        except Exception as e:
            print(f"❌ 通用方法失败: {e}")
    
    async def save_to_csv(self, filename="leboncoin_properties.csv"):
        """保存数据到CSV文件"""
        if not self.properties:
            print("❌ 没有数据可保存")
            return
        
        print(f"💾 正在保存 {len(self.properties)} 条记录到 {filename}")
        
        # 获取所有字段名
        all_fields = set()
        for prop in self.properties:
            all_fields.update(prop.keys())
        
        fieldnames = sorted(list(all_fields))
        
        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(self.properties)
        
        print(f"✅ 数据已保存到 {filename}")
    
    async def save_to_json(self, filename="leboncoin_properties.json"):
        """保存数据到JSON文件"""
        if not self.properties:
            return
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.properties, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 数据已保存到 {filename}")
    
    async def close(self):
        """关闭浏览器"""
        if self.browser:
            await self.browser.close()

async def main():
    """主函数"""
    # LeBonCoin搜索URL
    search_url = "https://www.leboncoin.fr/recherche?category=9&text=NOT%20construire&locations=Palaiseau_91120__48.71303_2.24628_4002&immo_sell_type=old&real_estate_type=1,2&owner_type=all"
    
    print("🏠 LeBonCoin房产爬虫 - 简化版")
    print("="*50)
    
    scraper = SimpleLeBonCoinScraper()
    
    try:
        await scraper.init_browser()
        
        if await scraper.navigate_to_search(search_url):
            await scraper.extract_properties()
            
            if scraper.properties:
                await scraper.save_to_csv()
                await scraper.save_to_json()
                
                print("\n📊 爬取结果:")
                print(f"总共获取: {len(scraper.properties)} 个房产")
                
                # 显示前3个房产的信息
                for i, prop in enumerate(scraper.properties[:3]):
                    print(f"\n房产 {i+1}:")
                    for key, value in prop.items():
                        if key not in ['extracted_at', 'index']:
                            print(f"  {key}: {value}")
            else:
                print("❌ 未获取到任何房产信息")
        
    except Exception as e:
        print(f"❌ 爬取过程中出现错误: {e}")
    
    finally:
        await scraper.close()

if __name__ == "__main__":
    asyncio.run(main())
