"""
LeBonCoin房产详情页爬虫
用于enrichment房源信息
"""

import asyncio
import pandas as pd
from typing import List, Dict, Any, Optional
from playwright.async_api import async_playwright, Page, Browser
from playwright_stealth import stealth_async

from config import *
from utils import *

class LeBonCoinDetailScraper:
    def __init__(self):
        self.logger = setup_logging(OUTPUT_CONFIG["logs_dir"])
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None
    
    async def init_browser(self) -> None:
        """初始化浏览器"""
        self.logger.info("正在初始化详情页爬虫浏览器...")
        
        playwright = await async_playwright().start()
        
        self.browser = await playwright.chromium.launch(
            headless=BROWSER_CONFIG["headless"],
            args=[
                '--no-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-dev-shm-usage',
            ]
        )
        
        context = await self.browser.new_context(
            viewport=BROWSER_CONFIG["viewport"],
            user_agent=get_random_user_agent(),
            locale=BROWSER_CONFIG["locale"],
            timezone_id=BROWSER_CONFIG["timezone_id"]
        )
        
        self.page = await context.new_page()
        await stealth_async(self.page)
        
        self.logger.info("详情页爬虫浏览器初始化完成")
    
    async def scrape_property_detail(self, property_url: str) -> Dict[str, Any]:
        """爬取单个房产的详细信息"""
        detail_data = {}
        
        try:
            self.logger.info(f"正在爬取详情页: {property_url}")
            
            # 访问详情页
            await self.page.goto(property_url, wait_until="networkidle")
            await random_delay(*DELAYS["page_load"])
            
            # 模拟人类行为
            await simulate_human_behavior(self.page)
            
            # 提取详细描述
            description = await self.extract_description()
            detail_data["description"] = description
            
            # 提取所有图片
            images = await self.extract_all_images()
            detail_data["all_images"] = images
            
            # 提取详细属性
            attributes = await self.extract_detailed_attributes()
            detail_data.update(attributes)
            
            # 提取卖家信息
            seller_info = await self.extract_seller_info()
            detail_data.update(seller_info)
            
            # 提取联系信息
            contact_info = await self.extract_contact_info()
            detail_data["contact_info"] = contact_info
            
            self.logger.info(f"详情页爬取完成: {property_url}")
            return detail_data
            
        except Exception as e:
            self.logger.error(f"爬取详情页失败 {property_url}: {e}")
            return {}
    
    async def extract_description(self) -> str:
        """提取房产描述"""
        try:
            desc_element = await self.page.query_selector(DETAIL_SELECTORS["description"])
            if desc_element:
                return await desc_element.inner_text()
        except Exception as e:
            self.logger.warning(f"提取描述失败: {e}")
        return ""
    
    async def extract_all_images(self) -> List[str]:
        """提取所有图片链接"""
        images = []
        try:
            img_elements = await self.page.query_selector_all(DETAIL_SELECTORS["all_images"])
            for img in img_elements:
                src = await img.get_attribute("src")
                if src:
                    images.append(normalize_url(src, BASE_URL))
        except Exception as e:
            self.logger.warning(f"提取图片失败: {e}")
        return images
    
    async def extract_detailed_attributes(self) -> Dict[str, Any]:
        """提取详细属性信息"""
        attributes = {}
        
        try:
            attr_elements = await self.page.query_selector_all(DETAIL_SELECTORS["attributes"])
            
            for element in attr_elements:
                text = await element.inner_text()
                
                # 解析各种属性
                if "chambre" in text.lower():
                    bedrooms = extract_rooms(text)
                    if bedrooms:
                        attributes["bedrooms"] = bedrooms
                
                if "année" in text.lower() or "construit" in text.lower():
                    year_match = re.search(r'(\d{4})', text)
                    if year_match:
                        attributes["year_built"] = int(year_match.group(1))
                
                if "dpe" in text.lower() or "énergie" in text.lower():
                    energy_match = re.search(r'([A-G])', text.upper())
                    if energy_match:
                        attributes["energy_class"] = energy_match.group(1)
                
                if "étage" in text.lower():
                    floor_match = re.search(r'(\d+)', text)
                    if floor_match:
                        attributes["floor"] = int(floor_match.group(1))
                
                if "balcon" in text.lower():
                    attributes["has_balcony"] = True
                
                if "parking" in text.lower() or "garage" in text.lower():
                    attributes["has_parking"] = True
                
                if "cave" in text.lower():
                    attributes["has_cellar"] = True
                
                if "ascenseur" in text.lower():
                    attributes["has_elevator"] = True
        
        except Exception as e:
            self.logger.warning(f"提取详细属性失败: {e}")
        
        return attributes
    
    async def extract_seller_info(self) -> Dict[str, Any]:
        """提取卖家信息"""
        seller_info = {}
        
        try:
            seller_element = await self.page.query_selector(DETAIL_SELECTORS["seller_info"])
            if seller_element:
                seller_text = await seller_element.inner_text()
                
                # 判断卖家类型
                if "particulier" in seller_text.lower():
                    seller_info["seller_type"] = "Particulier"
                elif "professionnel" in seller_text.lower() or "agence" in seller_text.lower():
                    seller_info["seller_type"] = "Professionnel"
                else:
                    seller_info["seller_type"] = "Unknown"
                
                # 提取卖家名称
                lines = seller_text.split('\n')
                if lines:
                    seller_info["seller_name"] = lines[0].strip()
        
        except Exception as e:
            self.logger.warning(f"提取卖家信息失败: {e}")
        
        return seller_info
    
    async def extract_contact_info(self) -> str:
        """提取联系信息"""
        try:
            contact_button = await self.page.query_selector(DETAIL_SELECTORS["contact_button"])
            if contact_button:
                # 点击联系按钮可能会显示更多信息
                await contact_button.click()
                await random_delay(1, 2)
                
                # 这里可以添加更多逻辑来提取显示的联系信息
                # 注意：实际的联系信息可能需要特殊处理
                return "Contact available"
        except Exception as e:
            self.logger.warning(f"提取联系信息失败: {e}")
        
        return ""
    
    async def enrich_properties_data(self, csv_file: str) -> None:
        """enrichment现有的房产数据"""
        try:
            # 读取现有数据
            df = pd.read_csv(csv_file)
            self.logger.info(f"读取到 {len(df)} 条房产数据")
            
            enriched_data = []
            
            for index, row in df.iterrows():
                property_url = row.get('url')
                
                if not property_url or not is_valid_property_url(property_url):
                    self.logger.warning(f"跳过无效URL: {property_url}")
                    continue
                
                # 获取详细信息
                detail_data = await self.scrape_property_detail(property_url)
                
                # 合并数据
                enriched_row = row.to_dict()
                enriched_row.update(detail_data)
                enriched_data.append(enriched_row)
                
                # 随机延迟避免被检测
                await random_delay(*DELAYS["between_requests"])
                
                # 每处理10个房产就保存一次，避免数据丢失
                if (index + 1) % 10 == 0:
                    temp_df = pd.DataFrame(enriched_data)
                    temp_df.to_csv(f"temp_{OUTPUT_CONFIG['detailed_csv_filename']}", 
                                 index=False, encoding='utf-8-sig')
                    self.logger.info(f"已处理 {index + 1} 个房产，临时保存完成")
            
            # 保存最终结果
            if enriched_data:
                save_to_csv(enriched_data, OUTPUT_CONFIG["detailed_csv_filename"])
                self.logger.info(f"enrichment完成，共处理 {len(enriched_data)} 个房产")
            
        except Exception as e:
            self.logger.error(f"enrichment数据时出错: {e}")
    
    async def close(self) -> None:
        """关闭浏览器"""
        if self.browser:
            await self.browser.close()
            self.logger.info("详情页爬虫浏览器已关闭")

async def main():
    """主函数 - 运行详情页爬虫"""
    scraper = LeBonCoinDetailScraper()
    
    try:
        # 检查是否存在基础数据文件
        if not os.path.exists(OUTPUT_CONFIG["csv_filename"]):
            print(f"错误：未找到基础数据文件 {OUTPUT_CONFIG['csv_filename']}")
            print("请先运行 main.py 获取房产列表数据")
            return
        
        # 初始化浏览器
        await scraper.init_browser()
        
        # enrichment房产数据
        await scraper.enrich_properties_data(OUTPUT_CONFIG["csv_filename"])
        
    except Exception as e:
        scraper.logger.error(f"详情页爬取过程中出现错误: {e}")
    
    finally:
        await scraper.close()

if __name__ == "__main__":
    asyncio.run(main())
