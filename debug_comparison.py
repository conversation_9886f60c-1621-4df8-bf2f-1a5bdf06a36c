#!/usr/bin/env python3
"""
调试对比脚本
对比不同配置访问LeBonCoin的结果
"""

import asyncio
from playwright.async_api import async_playwright
from playwright_stealth import stealth_async

async def test_configuration(config_name, browser_args=None, context_options=None, use_stealth=False):
    """测试特定配置"""
    print(f"\n{'='*20} {config_name} {'='*20}")
    
    try:
        playwright = await async_playwright().start()
        
        # 启动浏览器
        if browser_args:
            browser = await playwright.chromium.launch(headless=False, args=browser_args)
        else:
            browser = await playwright.chromium.launch(headless=False)
        
        # 创建上下文
        if context_options:
            context = await browser.new_context(**context_options)
            page = await context.new_page()
        else:
            page = await browser.new_page()
        
        # 应用stealth
        if use_stealth:
            await stealth_async(page)
        
        # 访问LeBonCoin
        print("🌐 正在访问LeBonCoin...")
        await page.goto("https://www.leboncoin.fr", timeout=30000)
        await asyncio.sleep(5)
        
        # 获取页面信息
        title = await page.title()
        content = await page.content()
        url = page.url
        
        print(f"📄 页面标题: {title}")
        print(f"🔗 当前URL: {url}")
        
        # 检查页面状态
        if "please enable js" in content.lower():
            print("❌ 遇到JS检测页面")
            status = "JS_BLOCKED"
        elif "cloudflare" in content.lower():
            print("🔒 遇到Cloudflare验证")
            status = "CLOUDFLARE"
        elif "verification" in content.lower() or "challenge" in content.lower():
            print("🔒 遇到验证页面")
            status = "VERIFICATION"
        elif len(content) > 10000 and "leboncoin" in content.lower():
            print("✅ 成功访问")
            status = "SUCCESS"
        else:
            print("⚠️  未知状态")
            status = "UNKNOWN"
        
        # 保存截图
        screenshot_name = f"debug_{config_name.lower().replace(' ', '_')}.png"
        await page.screenshot(path=screenshot_name)
        print(f"📸 截图已保存: {screenshot_name}")
        
        # 保存HTML
        html_name = f"debug_{config_name.lower().replace(' ', '_')}.html"
        with open(html_name, "w", encoding="utf-8") as f:
            f.write(content)
        print(f"💾 HTML已保存: {html_name}")
        
        await browser.close()
        await playwright.stop()
        
        return status
        
    except Exception as e:
        print(f"❌ 配置 {config_name} 测试失败: {e}")
        return "ERROR"

async def main():
    """主函数"""
    print("🔍 LeBonCoin访问配置对比测试")
    print("="*60)
    
    # 测试配置列表
    configurations = [
        {
            "name": "基础配置",
            "browser_args": None,
            "context_options": None,
            "use_stealth": False
        },
        {
            "name": "基础配置+Stealth",
            "browser_args": None,
            "context_options": None,
            "use_stealth": True
        },
        {
            "name": "反检测参数",
            "browser_args": [
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-blink-features=AutomationControlled'
            ],
            "context_options": None,
            "use_stealth": False
        },
        {
            "name": "反检测参数+Stealth",
            "browser_args": [
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-blink-features=AutomationControlled'
            ],
            "context_options": None,
            "use_stealth": True
        },
        {
            "name": "完整反检测配置",
            "browser_args": [
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-blink-features=AutomationControlled'
            ],
            "context_options": {
                'viewport': {'width': 1366, 'height': 768},
                'user_agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'locale': 'fr-FR',
                'timezone_id': 'Europe/Paris'
            },
            "use_stealth": True
        }
    ]
    
    results = {}
    
    # 测试每个配置
    for config in configurations:
        result = await test_configuration(
            config["name"],
            config["browser_args"],
            config["context_options"],
            config["use_stealth"]
        )
        results[config["name"]] = result
        
        # 等待一段时间再测试下一个配置
        print("⏳ 等待10秒后测试下一个配置...")
        await asyncio.sleep(10)
    
    # 显示结果汇总
    print("\n" + "="*60)
    print("📊 测试结果汇总:")
    print("="*60)
    
    for config_name, status in results.items():
        status_emoji = {
            "SUCCESS": "✅",
            "VERIFICATION": "🔒",
            "CLOUDFLARE": "🛡️",
            "JS_BLOCKED": "❌",
            "UNKNOWN": "⚠️",
            "ERROR": "💥"
        }.get(status, "❓")
        
        print(f"{status_emoji} {config_name}: {status}")
    
    # 推荐最佳配置
    print("\n📋 推荐:")
    success_configs = [name for name, status in results.items() if status in ["SUCCESS", "VERIFICATION"]]
    
    if success_configs:
        print(f"✅ 推荐使用: {success_configs[0]}")
        if "VERIFICATION" in [results[name] for name in success_configs]:
            print("💡 注意: 可能需要手动完成验证")
    else:
        print("❌ 所有配置都失败了，可能需要:")
        print("   1. 使用代理服务器")
        print("   2. 更换网络环境")
        print("   3. 等待一段时间后重试")
    
    print(f"\n📁 生成的文件:")
    for config in configurations:
        config_name = config["name"].lower().replace(' ', '_')
        print(f"   - debug_{config_name}.png")
        print(f"   - debug_{config_name}.html")

if __name__ == "__main__":
    asyncio.run(main())
