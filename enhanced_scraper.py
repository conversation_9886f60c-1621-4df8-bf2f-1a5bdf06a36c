#!/usr/bin/env python3
"""
增强版LeBonCoin房产爬虫
专门解决"Please enable J<PERSON> and disable any ad blocker"问题
"""

import asyncio
import csv
import json
import re
from datetime import datetime
from playwright.async_api import async_playwright
from playwright_stealth import stealth_async
from fake_useragent import UserAgent

class EnhancedLeBonCoinScraper:
    def __init__(self):
        self.browser = None
        self.page = None
        self.properties = []
        self.ua = UserAgent()
    
    async def init_browser(self):
        """初始化增强版浏览器"""
        print("🚀 正在启动增强版浏览器...")
        
        playwright = await async_playwright().start()
        
        # 更强的反检测配置
        self.browser = await playwright.chromium.launch(
            headless=False,  # 显示浏览器
            args=[
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-extensions-except',
                '--disable-plugins-discovery',
                '--no-first-run',
                '--no-default-browser-check',
                '--disable-default-apps',
                '--disable-popup-blocking',
                '--disable-translate',
                '--disable-background-timer-throttling',
                '--disable-renderer-backgrounding',
                '--disable-device-discovery-notifications',
                '--disable-ipc-flooding-protection',
                '--enable-features=NetworkService,NetworkServiceLogging',
                '--force-color-profile=srgb',
                '--disable-background-networking',
            ]
        )
        
        # 创建更真实的浏览器上下文
        context = await self.browser.new_context(
            viewport={'width': 1366, 'height': 768},  # 常见分辨率
            user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            locale='fr-FR',
            timezone_id='Europe/Paris',
            geolocation={'latitude': 48.8566, 'longitude': 2.3522},  # 巴黎坐标
            permissions=['geolocation'],
            color_scheme='light',
            reduced_motion='no-preference',
            forced_colors='none',
            extra_http_headers={
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-User': '?1',
                'Cache-Control': 'max-age=0',
            }
        )
        
        self.page = await context.new_page()
        
        # 应用stealth插件
        await stealth_async(self.page)
        
        # 注入额外的反检测脚本
        await self.page.add_init_script("""
            // 移除webdriver属性
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            // 伪造chrome对象
            window.chrome = {
                runtime: {},
                loadTimes: function() {},
                csi: function() {},
                app: {}
            };
            
            // 伪造插件
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            // 伪造语言
            Object.defineProperty(navigator, 'languages', {
                get: () => ['fr-FR', 'fr', 'en'],
            });
            
            // 移除自动化痕迹
            const originalQuery = window.document.querySelector;
            window.document.querySelector = function(selector) {
                if (selector.includes('webdriver') || selector.includes('automation')) {
                    return null;
                }
                return originalQuery.call(this, selector);
            };
            
            // 伪造屏幕信息
            Object.defineProperty(screen, 'colorDepth', {
                get: () => 24,
            });
            
            Object.defineProperty(screen, 'pixelDepth', {
                get: () => 24,
            });
        """)
        
        print("✅ 增强版浏览器启动成功")
    
    async def navigate_with_retry(self, url, max_retries=3):
        """带重试的导航"""
        for attempt in range(max_retries):
            try:
                print(f"🌐 尝试访问 (第{attempt+1}次): {url}")
                
                # 清除缓存和cookies
                await self.page.context.clear_cookies()
                
                # 访问页面
                response = await self.page.goto(
                    url, 
                    wait_until="domcontentloaded",
                    timeout=60000
                )
                
                print(f"📊 响应状态: {response.status}")
                
                # 等待页面稳定
                await asyncio.sleep(5)
                
                # 检查页面内容
                content = await self.page.content()
                title = await self.page.title()
                current_url = self.page.url
                
                print(f"📄 页面标题: {title}")
                print(f"🔗 当前URL: {current_url}")
                
                # 检查是否遇到反爬页面
                if any(keyword in content.lower() for keyword in [
                    'please enable js',
                    'disable any ad blocker',
                    'cloudflare',
                    'checking your browser',
                    'ddos protection',
                    'access denied'
                ]):
                    print(f"⚠️  检测到反爬页面，尝试绕过...")
                    await self.bypass_protection()
                    continue
                
                # 检查是否成功加载
                if 'leboncoin' in current_url.lower() and len(content) > 10000:
                    print("✅ 页面加载成功")
                    return True
                else:
                    print(f"❌ 页面加载异常，内容长度: {len(content)}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(10)  # 等待更长时间再重试
                        continue
                
            except Exception as e:
                print(f"❌ 访问失败 (第{attempt+1}次): {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(10)
                    continue
        
        return False
    
    async def bypass_protection(self):
        """绕过保护机制"""
        print("🛡️ 正在尝试绕过保护机制...")
        
        try:
            # 等待可能的验证完成
            await asyncio.sleep(10)
            
            # 尝试点击可能的验证按钮
            verification_selectors = [
                'input[type="checkbox"]',
                '[id*="challenge"]',
                '[class*="challenge"]',
                'button:has-text("Verify")',
                'button:has-text("Continue")',
                'button:has-text("Continuer")',
            ]
            
            for selector in verification_selectors:
                try:
                    element = await self.page.query_selector(selector)
                    if element:
                        print(f"🔘 找到验证元素: {selector}")
                        await element.click()
                        await asyncio.sleep(5)
                        break
                except:
                    continue
            
            # 模拟人类行为
            await self.simulate_human_behavior()
            
            # 等待页面重新加载
            await asyncio.sleep(10)
            
        except Exception as e:
            print(f"❌ 绕过保护失败: {e}")
    
    async def simulate_human_behavior(self):
        """模拟更真实的人类行为"""
        try:
            # 随机鼠标移动
            for _ in range(3):
                x = 100 + (300 * _)
                y = 100 + (200 * _)
                await self.page.mouse.move(x, y)
                await asyncio.sleep(0.5)
            
            # 随机滚动
            for _ in range(2):
                await self.page.evaluate("window.scrollBy(0, Math.random() * 500 + 200)")
                await asyncio.sleep(1)
            
            # 模拟按键
            await self.page.keyboard.press('Tab')
            await asyncio.sleep(0.5)
            
        except Exception as e:
            print(f"⚠️  人类行为模拟失败: {e}")
    
    async def wait_for_content_load(self):
        """等待内容加载"""
        print("⏳ 等待页面内容加载...")
        
        # 等待可能的房产元素出现
        selectors_to_wait = [
            "[data-test-id*='ad']",
            "article",
            ".ad-card",
            ".listing-item",
            "[class*='price']",
            "*:has-text('€')"
        ]
        
        for selector in selectors_to_wait:
            try:
                await self.page.wait_for_selector(selector, timeout=10000)
                print(f"✅ 检测到内容元素: {selector}")
                return True
            except:
                continue
        
        print("⚠️  未检测到预期内容，继续尝试...")
        return False
    
    async def extract_properties_enhanced(self):
        """增强版房产信息提取"""
        print("🏠 开始增强版房产信息提取...")
        
        # 等待内容加载
        await self.wait_for_content_load()
        await asyncio.sleep(5)
        
        # 保存页面截图用于调试
        await self.page.screenshot(path="current_page.png")
        print("📸 已保存页面截图: current_page.png")
        
        # 保存页面HTML用于分析
        content = await self.page.content()
        with open("current_page.html", "w", encoding="utf-8") as f:
            f.write(content)
        print("💾 已保存页面HTML: current_page.html")
        
        # 尝试多种方法提取房产信息
        methods = [
            self.extract_by_data_attributes,
            self.extract_by_class_names,
            self.extract_by_text_patterns,
            self.extract_by_generic_selectors
        ]
        
        for i, method in enumerate(methods):
            try:
                print(f"🔍 尝试提取方法 {i+1}: {method.__name__}")
                properties = await method()
                if properties:
                    self.properties.extend(properties)
                    print(f"✅ 方法 {i+1} 成功提取 {len(properties)} 个房产")
                    break
            except Exception as e:
                print(f"❌ 方法 {i+1} 失败: {e}")
                continue
        
        if not self.properties:
            print("❌ 所有提取方法都失败了")
            # 进行页面分析
            await self.analyze_page_structure()
    
    async def extract_by_data_attributes(self):
        """通过data属性提取"""
        selectors = [
            "[data-test-id*='ad']",
            "[data-testid*='ad']",
            "[data-qa*='ad']"
        ]
        
        for selector in selectors:
            elements = await self.page.query_selector_all(selector)
            if len(elements) >= 3:
                return await self.process_elements(elements, f"data-attribute: {selector}")
        return []
    
    async def extract_by_class_names(self):
        """通过类名提取"""
        selectors = [
            ".ad-card",
            ".adcard",
            ".listing-item",
            ".search-result",
            "[class*='ad-']",
            "[class*='listing']"
        ]
        
        for selector in selectors:
            elements = await self.page.query_selector_all(selector)
            if len(elements) >= 3:
                return await self.process_elements(elements, f"class-name: {selector}")
        return []
    
    async def extract_by_text_patterns(self):
        """通过文本模式提取"""
        # 查找包含价格的元素
        price_elements = await self.page.query_selector_all("*:has-text('€')")
        
        if len(price_elements) >= 5:
            # 获取这些元素的父容器
            containers = []
            for element in price_elements[:20]:
                try:
                    # 向上查找可能的容器
                    for level in range(1, 4):
                        parent = element
                        for _ in range(level):
                            parent = await parent.query_selector('xpath=..')
                            if not parent:
                                break
                        
                        if parent:
                            parent_text = await parent.inner_text()
                            if len(parent_text) > 50 and '€' in parent_text:
                                containers.append(parent)
                                break
                except:
                    continue
            
            if containers:
                return await self.process_elements(containers, "text-pattern: price containers")
        
        return []
    
    async def extract_by_generic_selectors(self):
        """通过通用选择器提取"""
        selectors = [
            "article",
            ".item",
            "[role='article']",
            "li",
            "div[class]"
        ]
        
        for selector in selectors:
            elements = await self.page.query_selector_all(selector)
            
            # 过滤出可能包含房产信息的元素
            valid_elements = []
            for element in elements:
                try:
                    text = await element.inner_text()
                    if len(text) > 30 and '€' in text:
                        valid_elements.append(element)
                except:
                    continue
            
            if len(valid_elements) >= 3:
                return await self.process_elements(valid_elements[:20], f"generic: {selector}")
        
        return []
    
    async def process_elements(self, elements, method_name):
        """处理元素列表"""
        print(f"📋 使用 {method_name} 处理 {len(elements)} 个元素")
        properties = []
        
        for i, element in enumerate(elements):
            try:
                property_data = await self.extract_single_property_enhanced(element, i)
                if property_data:
                    properties.append(property_data)
                    print(f"  ✅ 房产 {i+1}: {property_data.get('title', 'Unknown')[:30]}...")
            except Exception as e:
                print(f"  ❌ 房产 {i+1} 提取失败: {e}")
                continue
        
        return properties
    
    async def extract_single_property_enhanced(self, element, index):
        """增强版单个房产信息提取"""
        property_data = {
            'index': index + 1,
            'extracted_at': datetime.now().isoformat()
        }
        
        try:
            # 获取元素的所有文本内容
            full_text = await element.inner_text()
            
            # 提取价格
            price = self.extract_price_from_text(full_text)
            if price:
                property_data['price'] = price
            
            # 提取面积
            area = self.extract_area_from_text(full_text)
            if area:
                property_data['area'] = area
            
            # 提取房间数
            rooms = self.extract_rooms_from_text(full_text)
            if rooms:
                property_data['rooms'] = rooms
            
            # 提取位置
            location = self.extract_location_from_text(full_text)
            if location:
                property_data['location'] = location
            
            # 提取标题（取第一行非空文本）
            lines = [line.strip() for line in full_text.split('\n') if line.strip()]
            if lines:
                property_data['title'] = lines[0][:100]  # 限制长度
            
            # 提取链接
            link = await self.extract_link_from_element(element)
            if link:
                property_data['url'] = link
            
            # 提取图片
            image = await self.extract_image_from_element(element)
            if image:
                property_data['image'] = image
            
            # 计算平米价格
            if price and area and area > 0:
                property_data['price_per_sqm'] = round(price / area, 2)
            
            # 只返回包含关键信息的房产
            if price or property_data.get('title'):
                return property_data
            
        except Exception as e:
            print(f"❌ 提取房产 {index+1} 时出错: {e}")
        
        return None
    
    def extract_price_from_text(self, text):
        """从文本中提取价格"""
        patterns = [
            r'(\d{1,3}(?:\s\d{3})*)\s*€',
            r'(\d+(?:\.\d{3})*)\s*€',
            r'€\s*(\d{1,3}(?:\s\d{3})*)',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                try:
                    price_str = match.replace(' ', '').replace('.', '')
                    price = int(price_str)
                    if 10000 <= price <= 10000000:
                        return price
                except:
                    continue
        return None
    
    def extract_area_from_text(self, text):
        """从文本中提取面积"""
        patterns = [
            r'(\d+(?:[.,]\d+)?)\s*m[²2]',
            r'(\d+)\s*m[²2]'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                try:
                    area = float(match.replace(',', '.'))
                    if 10 <= area <= 1000:  # 合理的面积范围
                        return area
                except:
                    continue
        return None
    
    def extract_rooms_from_text(self, text):
        """从文本中提取房间数"""
        patterns = [
            r'(\d+)\s*pièces?',
            r'(\d+)\s*p\.',
            r'T(\d+)',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                try:
                    rooms = int(match)
                    if 1 <= rooms <= 20:  # 合理的房间数范围
                        return rooms
                except:
                    continue
        return None
    
    def extract_location_from_text(self, text):
        """从文本中提取位置"""
        patterns = [
            r'(\d{5})\s+([A-Za-zÀ-ÿ\s-]+)',
            r'([A-Za-zÀ-ÿ\s-]+)\s+\((\d{2,3})\)',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            if matches:
                return ' '.join(matches[0]).strip()
        return None
    
    async def extract_link_from_element(self, element):
        """从元素中提取链接"""
        try:
            link_element = await element.query_selector("a")
            if link_element:
                href = await link_element.get_attribute('href')
                if href:
                    if href.startswith('/'):
                        href = 'https://www.leboncoin.fr' + href
                    return href
        except:
            pass
        return None
    
    async def extract_image_from_element(self, element):
        """从元素中提取图片"""
        try:
            img_element = await element.query_selector("img")
            if img_element:
                src = await img_element.get_attribute('src')
                if src and ('http' in src or src.startswith('/')):
                    if src.startswith('/'):
                        src = 'https://www.leboncoin.fr' + src
                    return src
        except:
            pass
        return None
    
    async def analyze_page_structure(self):
        """分析页面结构"""
        print("🔍 分析页面结构...")
        
        try:
            # 统计页面元素
            all_elements = await self.page.query_selector_all("*")
            print(f"📊 页面总元素数: {len(all_elements)}")
            
            # 查找包含价格的元素
            price_elements = await self.page.query_selector_all("*:has-text('€')")
            print(f"💰 包含价格的元素: {len(price_elements)}")
            
            # 查找可能的容器元素
            containers = await self.page.query_selector_all("div, article, section, li")
            print(f"📦 容器元素数: {len(containers)}")
            
            # 分析页面文本
            page_text = await self.page.inner_text("body")
            if '€' in page_text:
                print("✅ 页面包含价格信息")
            else:
                print("❌ 页面不包含价格信息")
            
        except Exception as e:
            print(f"❌ 页面分析失败: {e}")
    
    async def save_results(self):
        """保存结果"""
        if not self.properties:
            print("❌ 没有数据可保存")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_filename = f"leboncoin_enhanced_{timestamp}.csv"
        json_filename = f"leboncoin_enhanced_{timestamp}.json"
        
        # 保存CSV
        if self.properties:
            fieldnames = set()
            for prop in self.properties:
                fieldnames.update(prop.keys())
            fieldnames = sorted(list(fieldnames))
            
            with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(self.properties)
            
            print(f"✅ CSV数据已保存: {csv_filename}")
        
        # 保存JSON
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(self.properties, f, indent=2, ensure_ascii=False)
        
        print(f"✅ JSON数据已保存: {json_filename}")
        print(f"📊 总共获取 {len(self.properties)} 个房产信息")
    
    async def close(self):
        """关闭浏览器"""
        if self.browser:
            await self.browser.close()

async def main():
    """主函数"""
    search_url = "https://www.leboncoin.fr/recherche?category=9&text=NOT%20construire&locations=Palaiseau_91120__48.71303_2.24628_4002&immo_sell_type=old&real_estate_type=1,2&owner_type=all"
    
    print("🚀 LeBonCoin增强版房产爬虫")
    print("专门解决反爬检测问题")
    print("="*50)
    
    scraper = EnhancedLeBonCoinScraper()
    
    try:
        await scraper.init_browser()
        
        if await scraper.navigate_with_retry(search_url):
            await scraper.extract_properties_enhanced()
            await scraper.save_results()
        else:
            print("❌ 无法访问目标页面")
    
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
    
    finally:
        await scraper.close()

if __name__ == "__main__":
    asyncio.run(main())
