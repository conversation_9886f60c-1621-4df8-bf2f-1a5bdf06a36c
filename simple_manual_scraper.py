#!/usr/bin/env python3
"""
简单手动验证版爬虫
完全复制anti_detection_test.py中成功的配置
"""

import asyncio
import csv
import json
import re
from datetime import datetime
from playwright.async_api import async_playwright

class SimpleManualScraper:
    def __init__(self):
        self.browser = None
        self.page = None
        self.properties = []
    
    async def init_browser(self):
        """初始化浏览器 - 完全复制测试脚本的成功配置"""
        print("🚀 正在启动浏览器...")
        
        playwright = await async_playwright().start()
        
        # 完全复制anti_detection_test.py中测试1的配置
        self.browser = await playwright.chromium.launch(headless=False)
        self.page = await self.browser.new_page()
        
        print("✅ 浏览器启动成功")
    
    async def navigate_to_leboncoin(self):
        """导航到LeBonCoin首页"""
        print("🌐 正在访问LeBonCoin首页...")
        
        try:
            await self.page.goto("https://www.leboncoin.fr", timeout=30000)
            await asyncio.sleep(5)
            
            title = await self.page.title()
            content = await self.page.content()
            
            print(f"📄 页面标题: {title}")
            
            if "please enable js" in content.lower():
                print("❌ 遇到JS检测页面")
                return False
            else:
                print("✅ 成功访问首页")
                await self.page.screenshot(path="leboncoin_homepage.png")
                return True
                
        except Exception as e:
            print(f"❌ 访问首页失败: {e}")
            return False
    
    async def navigate_to_search(self, search_url):
        """导航到搜索页面"""
        print("🔍 正在访问搜索页面...")
        
        try:
            await self.page.goto(search_url, timeout=30000)
            await asyncio.sleep(5)
            
            title = await self.page.title()
            content = await self.page.content()
            current_url = self.page.url
            
            print(f"📄 页面标题: {title}")
            print(f"🔗 当前URL: {current_url}")
            
            # 检查是否遇到验证页面
            if any(keyword in content.lower() for keyword in [
                'please enable js',
                'disable any ad blocker',
                'cloudflare',
                'checking your browser',
                'verification',
                'challenge'
            ]):
                print("🔒 检测到验证页面，等待手动处理...")
                await self.page.screenshot(path="verification_detected.png")
                
                # 等待用户手动处理验证
                await self.wait_for_manual_verification()
                return True
            else:
                print("✅ 直接访问成功")
                await self.page.screenshot(path="search_page_success.png")
                return True
                
        except Exception as e:
            print(f"❌ 访问搜索页面失败: {e}")
            return False
    
    async def wait_for_manual_verification(self):
        """等待手动验证"""
        print("\n" + "="*60)
        print("🔒 检测到验证页面！")
        print("📋 请在浏览器中手动完成以下操作：")
        print("   1. 完成滑块验证")
        print("   2. 点击任何验证按钮")
        print("   3. 等待页面加载完成")
        print("   4. 确保看到房产搜索结果")
        print("\n⏳ 程序将等待您完成验证...")
        print("   完成后请在终端按 Enter 键继续")
        print("="*60)
        
        # 等待用户按Enter
        input("\n按 Enter 键继续...")
        
        # 验证完成后等待页面稳定
        await asyncio.sleep(3)
        
        # 检查验证是否成功
        content = await self.page.content()
        title = await self.page.title()
        
        if any(keyword in content.lower() for keyword in [
            'please enable js',
            'disable any ad blocker',
            'verification'
        ]):
            print("⚠️  似乎验证还未完成，请继续尝试...")
            retry = input("是否重新等待验证？(y/n): ")
            if retry.lower() == 'y':
                await self.wait_for_manual_verification()
        else:
            print("✅ 验证完成！")
            await self.page.screenshot(path="after_manual_verification.png")
    
    async def extract_properties_simple(self):
        """简单的房产信息提取"""
        print("🏠 开始提取房产信息...")
        
        # 等待页面稳定
        await asyncio.sleep(5)
        
        # 保存当前页面
        await self.page.screenshot(path="extraction_start.png")
        content = await self.page.content()
        with open("extraction_page.html", "w", encoding="utf-8") as f:
            f.write(content)
        
        print("📸 已保存页面截图和HTML文件")
        
        # 查找包含价格的元素（最简单的方法）
        try:
            price_elements = await self.page.query_selector_all("*:has-text('€')")
            print(f"💰 找到 {len(price_elements)} 个包含价格的元素")
            
            if len(price_elements) < 5:
                print("⚠️  价格元素太少，可能页面未正确加载")
                print("请检查浏览器中的页面是否显示了房产列表")
                
                # 给用户机会手动操作
                manual_check = input("页面是否显示了房产列表？(y/n): ")
                if manual_check.lower() == 'n':
                    print("请在浏览器中手动导航到正确的搜索结果页面")
                    input("完成后按 Enter 继续...")
                    
                    # 重新获取元素
                    price_elements = await self.page.query_selector_all("*:has-text('€')")
                    print(f"💰 重新检查，找到 {len(price_elements)} 个包含价格的元素")
            
            # 处理前20个价格元素
            for i, element in enumerate(price_elements[:20]):
                try:
                    property_data = await self.extract_from_price_element(element, i)
                    if property_data:
                        self.properties.append(property_data)
                        print(f"  ✅ 房产 {len(self.properties)}: {property_data.get('price', 'Unknown')}€")
                except Exception as e:
                    print(f"  ❌ 处理元素 {i+1} 失败: {e}")
                    continue
            
            print(f"📊 总共提取到 {len(self.properties)} 个房产信息")
            
        except Exception as e:
            print(f"❌ 提取过程失败: {e}")
    
    async def extract_from_price_element(self, price_element, index):
        """从价格元素提取房产信息"""
        try:
            # 获取包含价格的父容器
            parent = price_element
            for level in range(3):  # 向上查找3级父元素
                try:
                    parent_candidate = await parent.query_selector('xpath=..')
                    if parent_candidate:
                        parent = parent_candidate
                    else:
                        break
                except:
                    break
            
            # 获取父容器的文本
            full_text = await parent.inner_text()
            
            if len(full_text) < 20:  # 文本太短，可能不是房产信息
                return None
            
            property_data = {
                'index': index + 1,
                'extracted_at': datetime.now().isoformat()
            }
            
            # 提取价格
            price = self.extract_price_from_text(full_text)
            if price:
                property_data['price'] = price
            else:
                return None  # 没有有效价格就跳过
            
            # 提取其他信息
            area = self.extract_area_from_text(full_text)
            if area:
                property_data['area'] = area
                property_data['price_per_sqm'] = round(price / area, 2)
            
            rooms = self.extract_rooms_from_text(full_text)
            if rooms:
                property_data['rooms'] = rooms
            
            location = self.extract_location_from_text(full_text)
            if location:
                property_data['location'] = location
            
            # 提取标题（第一行文本）
            lines = [line.strip() for line in full_text.split('\n') if line.strip()]
            if lines:
                property_data['title'] = lines[0][:100]
            
            # 尝试提取链接
            try:
                link_element = await parent.query_selector("a")
                if link_element:
                    href = await link_element.get_attribute('href')
                    if href:
                        if href.startswith('/'):
                            href = 'https://www.leboncoin.fr' + href
                        property_data['url'] = href
            except:
                pass
            
            return property_data
            
        except Exception as e:
            print(f"❌ 提取房产 {index+1} 失败: {e}")
            return None
    
    def extract_price_from_text(self, text):
        """从文本中提取价格"""
        patterns = [
            r'(\d{1,3}(?:\s\d{3})*)\s*€',
            r'(\d+(?:\.\d{3})*)\s*€',
            r'€\s*(\d{1,3}(?:\s\d{3})*)',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                try:
                    price_str = match.replace(' ', '').replace('.', '')
                    price = int(price_str)
                    if 10000 <= price <= 10000000:  # 合理的房价范围
                        return price
                except:
                    continue
        return None
    
    def extract_area_from_text(self, text):
        """从文本中提取面积"""
        patterns = [
            r'(\d+(?:[.,]\d+)?)\s*m[²2]',
            r'(\d+)\s*m[²2]'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                try:
                    area = float(match.replace(',', '.'))
                    if 10 <= area <= 1000:  # 合理的面积范围
                        return area
                except:
                    continue
        return None
    
    def extract_rooms_from_text(self, text):
        """从文本中提取房间数"""
        patterns = [
            r'(\d+)\s*pièces?',
            r'(\d+)\s*p\.',
            r'T(\d+)',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                try:
                    rooms = int(match)
                    if 1 <= rooms <= 20:  # 合理的房间数范围
                        return rooms
                except:
                    continue
        return None
    
    def extract_location_from_text(self, text):
        """从文本中提取位置"""
        patterns = [
            r'(\d{5})\s+([A-Za-zÀ-ÿ\s-]+)',
            r'([A-Za-zÀ-ÿ\s-]+)\s+\((\d{2,3})\)',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            if matches:
                return ' '.join(matches[0]).strip()
        return None
    
    async def save_results(self):
        """保存结果"""
        if not self.properties:
            print("❌ 没有数据可保存")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_filename = f"leboncoin_simple_{timestamp}.csv"
        json_filename = f"leboncoin_simple_{timestamp}.json"
        
        # 保存CSV
        fieldnames = set()
        for prop in self.properties:
            fieldnames.update(prop.keys())
        fieldnames = sorted(list(fieldnames))
        
        with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(self.properties)
        
        print(f"✅ CSV数据已保存: {csv_filename}")
        
        # 保存JSON
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(self.properties, f, indent=2, ensure_ascii=False)
        
        print(f"✅ JSON数据已保存: {json_filename}")
        print(f"📊 总共获取 {len(self.properties)} 个房产信息")
        
        # 显示前3个房产的信息
        for i, prop in enumerate(self.properties[:3]):
            print(f"\n房产 {i+1}:")
            for key, value in prop.items():
                if key not in ['extracted_at', 'index']:
                    print(f"  {key}: {value}")
    
    async def close(self):
        """关闭浏览器"""
        if self.browser:
            print("\n🔍 程序即将结束，浏览器将保持打开10秒供检查...")
            await asyncio.sleep(10)
            await self.browser.close()

async def main():
    """主函数"""
    search_url = "https://www.leboncoin.fr/recherche?category=9&text=NOT%20construire&locations=Palaiseau_91120__48.71303_2.24628_4002&immo_sell_type=old&real_estate_type=1,2&owner_type=all"
    
    print("🏠 简单手动验证版LeBonCoin爬虫")
    print("使用与测试脚本相同的成功配置")
    print("="*50)
    
    scraper = SimpleManualScraper()
    
    try:
        await scraper.init_browser()
        
        # 先访问首页测试
        if await scraper.navigate_to_leboncoin():
            # 然后访问搜索页面
            if await scraper.navigate_to_search(search_url):
                await scraper.extract_properties_simple()
                await scraper.save_results()
            else:
                print("❌ 无法访问搜索页面")
        else:
            print("❌ 无法访问LeBonCoin首页")
    
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
    
    finally:
        await scraper.close()

if __name__ == "__main__":
    asyncio.run(main())
