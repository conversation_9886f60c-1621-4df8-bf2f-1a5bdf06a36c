"""
配置文件 - 房产爬虫系统
"""

import os
from typing import Dict, List

# 基础配置
BASE_URL = "https://www.leboncoin.fr"
SEARCH_URL = "https://www.leboncoin.fr/recherche?category=9&text=NOT%20construire&locations=Palaiseau_91120__48.71303_2.24628_4002&immo_sell_type=old&real_estate_type=1,2&owner_type=all"

# 浏览器配置
BROWSER_CONFIG = {
    "headless": False,  # 设为False可以看到浏览器操作，调试时有用
    "viewport": {"width": 1920, "height": 1080},
    "user_agent": None,  # 将使用fake-useragent动态生成
    "locale": "fr-FR",
    "timezone_id": "Europe/Paris"
}

# 反检测配置
STEALTH_CONFIG = {
    "webdriver": True,
    "chrome_app": True,
    "chrome_csi": True,
    "chrome_load_times": True,
    "chrome_runtime": True,
    "iframe_content_window": True,
    "media_codecs": True,
    "navigator_languages": True,
    "navigator_permissions": True,
    "navigator_plugins": True,
    "navigator_vendor": True,
    "navigator_webdriver": True,
    "outerdimensions": True,
    "hairline": True
}

# 延迟配置（秒）
DELAYS = {
    "page_load": (2, 5),      # 页面加载后等待
    "between_requests": (1, 3), # 请求间隔
    "scroll": (0.5, 1.5),     # 滚动延迟
    "click": (0.3, 0.8)       # 点击延迟
}

# 重试配置
RETRY_CONFIG = {
    "max_retries": 3,
    "retry_delay": 5,
    "timeout": 30
}

# 输出配置
OUTPUT_CONFIG = {
    "csv_filename": "leboncoin_properties.csv",
    "detailed_csv_filename": "leboncoin_properties_detailed.csv",
    "images_dir": "property_images",
    "logs_dir": "logs"
}

# CSS选择器配置
SELECTORS = {
    "property_cards": "[data-test-id='adcard'], .adcard, .ad-card, .listing-item",
    "property_link": "a[data-test-id='ad-link'], .ad-link, .listing-link, a[href*='/ad/']",
    "property_image": "img[data-test-id='ad-image'], .ad-image, .listing-image, img[alt*='photo']",
    "property_price": "[data-test-id='price'], .price, .ad-price, .listing-price, [class*='price']",
    "property_title": "[data-test-id='ad-title'], .ad-title, .listing-title, h3, h2",
    "property_location": "[data-test-id='ad-location'], .ad-location, .listing-location, [class*='location']",
    "property_attributes": "[data-test-id='ad-attributes'], .ad-attributes, .listing-attributes, .criteria",
    "next_page": "[data-test-id='pagination-next'], .pagination-next, .next-page, [aria-label*='suivant']",
    "load_more": "[data-test-id='load-more'], .load-more, .show-more, [class*='load']"
}

# 详情页选择器
DETAIL_SELECTORS = {
    "description": "[data-test-id='ad-description']",
    "all_images": "[data-test-id='slideshow'] img",
    "attributes": "[data-test-id='ad-attributes'] li",
    "seller_info": "[data-test-id='seller-info']",
    "contact_button": "[data-test-id='contact-button']"
}

# 数据字段映射
FIELDS_MAPPING = {
    "url": "房源链接",
    "title": "标题",
    "price": "价格",
    "price_per_sqm": "平米价格",
    "area": "面积",
    "property_type": "房产类型",
    "location": "位置",
    "main_image": "主图链接",
    "seller_type": "卖家类型",
    "seller_name": "卖家名称",
    "rooms": "房间数",
    "bedrooms": "卧室数",
    "year_built": "建造年份",
    "energy_class": "能耗等级",
    "description": "描述",
    "all_images": "所有图片",
    "contact_info": "联系方式"
}
