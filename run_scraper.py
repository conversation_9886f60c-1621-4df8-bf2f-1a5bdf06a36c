#!/usr/bin/env python3
"""
LeBonCoin房产爬虫 - 一键运行脚本
"""

import asyncio
import os
import sys
import argparse
from datetime import datetime

def print_banner():
    """打印横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    LeBonCoin房产爬虫系统                      ║
║                  使用前沿反检测技术                           ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import playwright
        import pandas
        import fake_useragent
        print("✅ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def check_playwright_browsers():
    """检查Playwright浏览器是否安装"""
    try:
        from playwright.sync_api import sync_playwright
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            browser.close()
        print("✅ Playwright浏览器已安装")
        return True
    except Exception as e:
        print(f"❌ Playwright浏览器未安装: {e}")
        print("请运行: playwright install chromium")
        return False

async def run_stage1():
    """运行第一阶段：爬取列表页"""
    print("\n🚀 开始第一阶段：爬取房产列表...")
    print("=" * 60)
    
    try:
        from main import main as stage1_main
        await stage1_main()
        print("\n✅ 第一阶段完成！")
        return True
    except Exception as e:
        print(f"\n❌ 第一阶段失败: {e}")
        return False

async def run_stage2():
    """运行第二阶段：enrichment详细信息"""
    print("\n🔍 开始第二阶段：enrichment详细信息...")
    print("=" * 60)
    
    try:
        from detail_scraper import main as stage2_main
        await stage2_main()
        print("\n✅ 第二阶段完成！")
        return True
    except Exception as e:
        print(f"\n❌ 第二阶段失败: {e}")
        return False

def show_results():
    """显示结果"""
    from config import OUTPUT_CONFIG
    
    print("\n📊 爬取结果:")
    print("=" * 60)
    
    # 检查基础数据文件
    basic_file = OUTPUT_CONFIG["csv_filename"]
    if os.path.exists(basic_file):
        import pandas as pd
        df = pd.read_csv(basic_file)
        print(f"📄 基础数据文件: {basic_file}")
        print(f"   - 房产数量: {len(df)}")
        print(f"   - 文件大小: {os.path.getsize(basic_file) / 1024:.1f} KB")
    
    # 检查详细数据文件
    detailed_file = OUTPUT_CONFIG["detailed_csv_filename"]
    if os.path.exists(detailed_file):
        df_detailed = pd.read_csv(detailed_file)
        print(f"📄 详细数据文件: {detailed_file}")
        print(f"   - 房产数量: {len(df_detailed)}")
        print(f"   - 文件大小: {os.path.getsize(detailed_file) / 1024:.1f} KB")
    
    # 检查日志文件
    log_file = os.path.join(OUTPUT_CONFIG["logs_dir"], "scraper.log")
    if os.path.exists(log_file):
        print(f"📋 日志文件: {log_file}")
        print(f"   - 文件大小: {os.path.getsize(log_file) / 1024:.1f} KB")

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="LeBonCoin房产爬虫")
    parser.add_argument("--stage", choices=["1", "2", "all"], default="all",
                       help="运行阶段: 1=列表页, 2=详情页, all=全部")
    parser.add_argument("--skip-checks", action="store_true",
                       help="跳过依赖检查")
    
    args = parser.parse_args()
    
    print_banner()
    
    # 检查依赖
    if not args.skip_checks:
        print("🔍 检查系统环境...")
        if not check_dependencies():
            sys.exit(1)
        if not check_playwright_browsers():
            sys.exit(1)
    
    start_time = datetime.now()
    
    try:
        if args.stage in ["1", "all"]:
            success = await run_stage1()
            if not success and args.stage == "all":
                print("❌ 第一阶段失败，停止执行")
                return
        
        if args.stage in ["2", "all"]:
            # 检查是否存在基础数据
            from config import OUTPUT_CONFIG
            if not os.path.exists(OUTPUT_CONFIG["csv_filename"]):
                print(f"❌ 未找到基础数据文件: {OUTPUT_CONFIG['csv_filename']}")
                print("请先运行第一阶段或使用 --stage 1")
                return
            
            await run_stage2()
        
        # 显示结果
        show_results()
        
        end_time = datetime.now()
        duration = end_time - start_time
        print(f"\n⏱️  总耗时: {duration}")
        print("\n🎉 爬取任务完成！")
        
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断了程序")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
