# LeBonCoin房产爬虫 - 安装指南

## 📋 系统要求

- Python 3.8 或更高版本
- 至少 2GB 可用内存
- 稳定的网络连接
- macOS, Windows, 或 Linux

## 🚀 快速安装

### 1. 安装Python依赖

```bash
# 方法1：使用pip直接安装
pip install playwright playwright-stealth pandas fake-useragent beautifulsoup4 requests

# 方法2：使用requirements.txt
pip install -r requirements.txt
```

### 2. 安装Playwright浏览器

```bash
# 安装Chromium浏览器
playwright install chromium

# 如果上面命令失败，尝试：
python -m playwright install chromium
```

### 3. 验证安装

```bash
# 运行测试脚本验证安装
python test_scraper.py
```

## 🎯 使用步骤

### 步骤1：测试系统
```bash
python test_scraper.py
```
这个脚本会：
- 测试浏览器是否正常启动
- 测试网站访问是否成功
- 测试数据提取功能
- 生成调试信息

### 步骤2：运行爬虫
```bash
# 推荐：使用简化版爬虫
python simple_scraper.py

# 或者使用完整版
python main.py
```

### 步骤3：查看结果
爬虫会生成以下文件：
- `leboncoin_properties.csv` - 房产数据（CSV格式）
- `leboncoin_properties.json` - 房产数据（JSON格式）
- `logs/scraper.log` - 运行日志

## 🔧 故障排除

### 常见问题1：浏览器启动失败
```bash
# 错误信息：Executable doesn't exist
# 解决方案：重新安装浏览器
playwright install chromium --force
```

### 常见问题2：网站访问被阻止
```bash
# 解决方案1：更新选择器
python selector_finder.py

# 解决方案2：检查网络连接
curl -I https://www.leboncoin.fr
```

### 常见问题3：找不到房产信息
```bash
# 运行选择器检测工具
python selector_finder.py

# 查看生成的调试文件
# - page_structure.html
# - debug_screenshot.png
# - found_selectors.json
```

### 常见问题4：权限错误
```bash
# macOS/Linux
sudo pip install playwright
sudo playwright install chromium

# Windows（以管理员身份运行）
pip install playwright
playwright install chromium
```

## 📊 输出文件说明

### CSV文件字段
- `index` - 房产序号
- `title` - 房产标题
- `price` - 价格（欧元）
- `area` - 面积（平方米）
- `rooms` - 房间数
- `price_per_sqm` - 平米价格
- `location` - 位置信息
- `url` - 房产链接
- `image` - 主图链接
- `extracted_at` - 提取时间

### 日志文件
位置：`logs/scraper.log`
包含：
- 运行时间戳
- 成功/失败信息
- 错误详情
- 调试信息

## ⚙️ 高级配置

### 修改搜索条件
编辑 `simple_scraper.py` 中的 `search_url` 变量：

```python
# 示例：搜索巴黎的公寓
search_url = "https://www.leboncoin.fr/recherche?category=9&locations=Paris_75000&real_estate_type=1"

# 示例：搜索特定价格范围
search_url = "https://www.leboncoin.fr/recherche?category=9&price=200000-500000"
```

### 调整爬取参数
在脚本中修改以下参数：

```python
# 修改headless模式（True=隐藏浏览器，False=显示浏览器）
headless=True

# 修改处理的房产数量
elements[:20]  # 改为你想要的数量

# 修改延迟时间
await asyncio.sleep(3)  # 增加延迟避免被检测
```

## 🛡️ 反爬策略

### 1. 随机延迟
```python
import random
await asyncio.sleep(random.uniform(2, 5))
```

### 2. 轮换User-Agent
```python
from fake_useragent import UserAgent
ua = UserAgent()
user_agent = ua.chrome
```

### 3. 使用代理（可选）
```python
context = await browser.new_context(
    proxy={"server": "http://proxy-server:port"}
)
```

## 📝 开发说明

### 添加新字段
在 `extract_single_property` 方法中添加：

```python
# 提取新字段
new_field = await self.find_new_field_in_element(element)
if new_field:
    property_data['new_field'] = new_field
```

### 更新选择器
1. 运行 `selector_finder.py` 分析页面
2. 查看 `found_selectors.json` 结果
3. 更新 `config.py` 中的选择器

### 调试模式
设置 `headless=False` 可以看到浏览器操作过程，便于调试。

## 📞 技术支持

如果遇到问题：

1. 首先运行 `python test_scraper.py` 诊断问题
2. 查看 `logs/scraper.log` 日志文件
3. 运行 `python selector_finder.py` 更新选择器
4. 检查网络连接和防火墙设置

## ⚖️ 法律声明

- 本工具仅供学习和研究使用
- 请遵守网站的robots.txt和使用条款
- 不要过于频繁地请求，避免对服务器造成压力
- 爬取的数据仅供个人使用，不得用于商业目的

## 🔄 更新日志

- v1.0 - 初始版本，基础爬取功能
- v1.1 - 添加反检测功能
- v1.2 - 添加智能选择器检测
- v1.3 - 添加简化版爬虫，提高稳定性
