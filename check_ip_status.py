#!/usr/bin/env python3
"""
IP状态检查工具
检查当前IP是否被LeBonCoin封禁
"""

import asyncio
import requests
from datetime import datetime
from playwright.async_api import async_playwright

async def check_ip_with_playwright():
    """使用Playwright检查IP状态"""
    print("🔍 使用Playwright检查IP状态...")
    
    try:
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=True)
        page = await browser.new_page()
        
        # 获取当前IP
        await page.goto("https://httpbin.org/ip", timeout=15000)
        ip_content = await page.content()
        
        if '"origin"' in ip_content:
            import json
            ip_data = json.loads(await page.inner_text('pre'))
            current_ip = ip_data.get('origin', 'Unknown')
            print(f"📍 当前IP: {current_ip}")
        
        # 测试LeBonCoin
        print("🌐 测试LeBonCoin访问...")
        await page.goto("https://www.leboncoin.fr", timeout=30000)
        await asyncio.sleep(3)
        
        content = await page.content()
        title = await page.title()
        url = page.url
        
        print(f"📄 页面标题: {title}")
        print(f"🔗 当前URL: {url}")
        
        # 保存页面截图
        await page.screenshot(path="ip_status_check.png")
        
        # 分析状态
        if "pourquoi ce blocage" in content.lower() or "blocage" in content.lower():
            print("❌ IP被封禁")
            print("📋 封禁原因页面内容:")
            
            # 提取封禁信息
            if "ip" in content.lower():
                import re
                ip_matches = re.findall(r'IP\s+(\d+\.\d+\.\d+\.\d+)', content)
                if ip_matches:
                    print(f"   被封IP: {ip_matches[0]}")
            
            status = "BLOCKED"
        elif "please enable js" in content.lower():
            print("🔒 遇到JS验证页面（IP可用但需验证）")
            status = "VERIFICATION_REQUIRED"
        elif "cloudflare" in content.lower():
            print("🛡️ 遇到Cloudflare保护")
            status = "CLOUDFLARE_PROTECTION"
        elif len(content) > 5000 and "leboncoin" in content.lower():
            print("✅ IP状态正常")
            status = "NORMAL"
        else:
            print("⚠️  未知状态")
            status = "UNKNOWN"
        
        await browser.close()
        await playwright.stop()
        
        return status, current_ip
        
    except Exception as e:
        print(f"❌ Playwright检查失败: {e}")
        return "ERROR", "Unknown"

def check_ip_with_requests():
    """使用requests检查IP状态"""
    print("🔍 使用requests检查IP状态...")
    
    try:
        # 获取当前IP
        ip_response = requests.get("https://httpbin.org/ip", timeout=10)
        if ip_response.status_code == 200:
            ip_data = ip_response.json()
            current_ip = ip_data.get('origin', 'Unknown')
            print(f"📍 当前IP: {current_ip}")
        else:
            current_ip = "Unknown"
        
        # 测试LeBonCoin
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
        
        response = requests.get("https://www.leboncoin.fr", headers=headers, timeout=15)
        
        print(f"📊 HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text.lower()
            
            if "pourquoi ce blocage" in content or "blocage" in content:
                print("❌ IP被封禁")
                return "BLOCKED", current_ip
            elif "please enable js" in content:
                print("🔒 需要JS验证")
                return "VERIFICATION_REQUIRED", current_ip
            else:
                print("✅ 基础访问正常")
                return "NORMAL", current_ip
        else:
            print(f"⚠️  HTTP错误: {response.status_code}")
            return "HTTP_ERROR", current_ip
            
    except Exception as e:
        print(f"❌ requests检查失败: {e}")
        return "ERROR", "Unknown"

def get_unblock_suggestions(status):
    """获取解封建议"""
    suggestions = {
        "BLOCKED": [
            "⏰ 等待15分钟到几小时自动解封",
            "🌐 使用VPN或代理服务器",
            "📱 切换到手机热点网络",
            "🏠 更换网络环境（如从家里换到办公室）",
            "⏳ 等待24小时后重试"
        ],
        "VERIFICATION_REQUIRED": [
            "🤖 使用手动验证版爬虫",
            "⏳ 降低访问频率",
            "🎭 更换User-Agent",
            "🔄 清除浏览器缓存和cookies"
        ],
        "CLOUDFLARE_PROTECTION": [
            "🛡️ 使用更强的反检测技术",
            "🌐 使用住宅代理IP",
            "⏰ 等待保护级别降低",
            "🔄 尝试不同的访问路径"
        ],
        "NORMAL": [
            "✅ IP状态正常，可以开始爬取",
            "⚠️ 建议使用较慢的访问频率",
            "🤖 推荐使用手动验证版爬虫"
        ]
    }
    
    return suggestions.get(status, ["❓ 未知状态，建议联系技术支持"])

async def monitor_ip_status(check_interval=300):
    """监控IP状态"""
    print(f"📊 开始监控IP状态（每{check_interval//60}分钟检查一次）")
    print("按 Ctrl+C 停止监控")
    
    try:
        while True:
            print(f"\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - 检查中...")
            
            status, ip = await check_ip_with_playwright()
            
            if status == "NORMAL":
                print("🎉 IP已解封！可以开始爬取")
                break
            elif status == "BLOCKED":
                print(f"🚫 IP仍被封禁，{check_interval//60}分钟后再次检查...")
            else:
                print(f"📊 当前状态: {status}")
            
            await asyncio.sleep(check_interval)
            
    except KeyboardInterrupt:
        print("\n⏹️  监控已停止")

async def main():
    """主函数"""
    print("🔍 LeBonCoin IP状态检查工具")
    print("="*40)
    
    # 选择检查方式
    print("选择检查方式:")
    print("1. 快速检查 (requests)")
    print("2. 详细检查 (playwright)")
    print("3. 持续监控")
    
    choice = input("请选择 (1/2/3): ")
    
    if choice == "1":
        status, ip = check_ip_with_requests()
    elif choice == "2":
        status, ip = await check_ip_with_playwright()
    elif choice == "3":
        await monitor_ip_status()
        return
    else:
        print("❌ 无效选择")
        return
    
    # 显示结果和建议
    print("\n" + "="*40)
    print("📊 检查结果:")
    print(f"   IP地址: {ip}")
    print(f"   状态: {status}")
    
    print("\n💡 建议:")
    suggestions = get_unblock_suggestions(status)
    for i, suggestion in enumerate(suggestions, 1):
        print(f"   {i}. {suggestion}")
    
    # 根据状态提供下一步操作
    if status == "BLOCKED":
        print("\n🚀 下一步操作:")
        print("   - 运行: python proxy_scraper.py (使用代理)")
        print("   - 或等待解封后运行: python simple_manual_scraper.py")
    elif status in ["VERIFICATION_REQUIRED", "NORMAL"]:
        print("\n🚀 下一步操作:")
        print("   - 运行: python simple_manual_scraper.py")
    
    print(f"\n📁 生成的文件:")
    print("   - ip_status_check.png (页面截图)")

if __name__ == "__main__":
    asyncio.run(main())
