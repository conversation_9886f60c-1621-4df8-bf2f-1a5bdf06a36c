#!/usr/bin/env python3
"""
LeBonCoin爬虫测试脚本
用于验证系统是否正常工作
"""

import asyncio
import sys
from playwright.async_api import async_playwright
from playwright_stealth import stealth_async
from config import *
from utils import *

async def test_browser_setup():
    """测试浏览器设置"""
    print("🔧 测试浏览器设置...")
    
    try:
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(
            headless=False,  # 显示浏览器以便观察
            args=[
                '--no-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-dev-shm-usage',
            ]
        )
        
        context = await browser.new_context(
            viewport=BROWSER_CONFIG["viewport"],
            user_agent=get_random_user_agent(),
            locale=BROWSER_CONFIG["locale"],
            timezone_id=BROWSER_CONFIG["timezone_id"]
        )
        
        page = await context.new_page()
        await stealth_async(page)
        
        print("✅ 浏览器设置成功")
        await browser.close()
        return True
        
    except Exception as e:
        print(f"❌ 浏览器设置失败: {e}")
        return False

async def test_website_access():
    """测试网站访问"""
    print("🌐 测试网站访问...")
    
    try:
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=False)
        context = await browser.new_context(
            user_agent=get_random_user_agent()
        )
        page = await context.new_page()
        await stealth_async(page)
        
        # 访问搜索页面
        print(f"正在访问: {SEARCH_URL}")
        await page.goto(SEARCH_URL, wait_until="networkidle", timeout=30000)
        
        # 检查页面标题
        title = await page.title()
        print(f"页面标题: {title}")
        
        # 检查是否被重定向或阻止
        current_url = page.url
        print(f"当前URL: {current_url}")
        
        if "leboncoin.fr" in current_url:
            print("✅ 网站访问成功")
            
            # 尝试查找房产卡片
            await asyncio.sleep(3)  # 等待页面加载
            
            # 尝试多个选择器
            selectors_to_try = SELECTORS["property_cards"].split(", ")
            found_elements = False
            
            for selector in selectors_to_try:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements:
                        print(f"✅ 找到 {len(elements)} 个房产卡片 (使用选择器: {selector})")
                        found_elements = True
                        break
                except:
                    continue
            
            if not found_elements:
                print("⚠️  未找到房产卡片，可能需要更新选择器")
                # 保存页面截图用于调试
                await page.screenshot(path="debug_screenshot.png")
                print("已保存调试截图: debug_screenshot.png")
            
            await browser.close()
            return True
        else:
            print("❌ 可能被重定向或阻止")
            await browser.close()
            return False
            
    except Exception as e:
        print(f"❌ 网站访问失败: {e}")
        return False

async def test_data_extraction():
    """测试数据提取"""
    print("📊 测试数据提取...")
    
    try:
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=False)
        context = await browser.new_context(
            user_agent=get_random_user_agent()
        )
        page = await context.new_page()
        await stealth_async(page)
        
        await page.goto(SEARCH_URL, wait_until="networkidle", timeout=30000)
        await asyncio.sleep(5)  # 等待页面完全加载
        
        # 尝试提取第一个房产的信息
        selectors_to_try = SELECTORS["property_cards"].split(", ")
        
        for selector in selectors_to_try:
            try:
                cards = await page.query_selector_all(selector)
                if cards and len(cards) > 0:
                    print(f"找到 {len(cards)} 个房产卡片")
                    
                    # 提取第一个房产的信息
                    first_card = cards[0]
                    
                    # 提取链接
                    link_selectors = SELECTORS["property_link"].split(", ")
                    for link_sel in link_selectors:
                        try:
                            link_element = await first_card.query_selector(link_sel)
                            if link_element:
                                href = await link_element.get_attribute("href")
                                if href:
                                    print(f"✅ 房源链接: {href}")
                                    break
                        except:
                            continue
                    
                    # 提取标题
                    title_selectors = SELECTORS["property_title"].split(", ")
                    for title_sel in title_selectors:
                        try:
                            title_element = await first_card.query_selector(title_sel)
                            if title_element:
                                title = await title_element.inner_text()
                                if title:
                                    print(f"✅ 房源标题: {title}")
                                    break
                        except:
                            continue
                    
                    # 提取价格
                    price_selectors = SELECTORS["property_price"].split(", ")
                    for price_sel in price_selectors:
                        try:
                            price_element = await first_card.query_selector(price_sel)
                            if price_element:
                                price = await price_element.inner_text()
                                if price:
                                    print(f"✅ 房源价格: {price}")
                                    break
                        except:
                            continue
                    
                    print("✅ 数据提取测试成功")
                    await browser.close()
                    return True
                    
            except Exception as e:
                print(f"选择器 {selector} 失败: {e}")
                continue
        
        print("❌ 数据提取失败")
        await browser.close()
        return False
        
    except Exception as e:
        print(f"❌ 数据提取测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始LeBonCoin爬虫系统测试")
    print("=" * 50)
    
    tests = [
        ("浏览器设置", test_browser_setup),
        ("网站访问", test_website_access),
        ("数据提取", test_data_extraction),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 运行测试: {test_name}")
        print("-" * 30)
        
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！系统准备就绪。")
        print("\n下一步:")
        print("1. 运行 python main.py 开始爬取")
        print("2. 或运行 python run_scraper.py 使用一键脚本")
    else:
        print("⚠️  部分测试失败，请检查配置和网络连接。")
        print("\n建议:")
        print("1. 检查网络连接")
        print("2. 更新CSS选择器")
        print("3. 检查反爬策略是否有变化")

if __name__ == "__main__":
    asyncio.run(main())
