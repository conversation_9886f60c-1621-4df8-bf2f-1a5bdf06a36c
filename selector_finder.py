#!/usr/bin/env python3
"""
智能选择器检测脚本
自动分析LeBonCoin页面结构并生成最佳选择器
"""

import asyncio
import json
from playwright.async_api import async_playwright
from playwright_stealth import stealth_async
from config import SEARCH_URL, BROWSER_CONFIG
from utils import get_random_user_agent

class SelectorFinder:
    def __init__(self):
        self.browser = None
        self.page = None
        self.found_selectors = {}
    
    async def init_browser(self):
        """初始化浏览器"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=False,  # 显示浏览器便于观察
            args=['--no-sandbox', '--disable-dev-shm-usage']
        )
        
        context = await self.browser.new_context(
            viewport=BROWSER_CONFIG["viewport"],
            user_agent=get_random_user_agent(),
            locale=BROWSER_CONFIG["locale"]
        )
        
        self.page = await context.new_page()
        await stealth_async(self.page)
    
    async def analyze_page_structure(self):
        """分析页面结构"""
        print("🔍 正在分析页面结构...")
        
        await self.page.goto(SEARCH_URL, wait_until="networkidle", timeout=30000)
        await asyncio.sleep(5)  # 等待页面完全加载
        
        # 保存页面HTML用于分析
        content = await self.page.content()
        with open("page_structure.html", "w", encoding="utf-8") as f:
            f.write(content)
        print("✅ 页面HTML已保存到 page_structure.html")
        
        # 分析可能的房产卡片容器
        await self.find_property_cards()
        
        # 如果找到了卡片，分析卡片内部结构
        if self.found_selectors.get("property_cards"):
            await self.analyze_card_structure()
    
    async def find_property_cards(self):
        """查找房产卡片容器"""
        print("🏠 正在查找房产卡片...")
        
        # 可能的卡片选择器
        potential_selectors = [
            "[data-test-id*='ad']",
            "[data-testid*='ad']",
            ".ad-card",
            ".adcard",
            ".listing-item",
            ".listing-card",
            ".property-card",
            ".search-result",
            "[class*='ad-']",
            "[class*='listing']",
            "[class*='card']",
            "article",
            ".item",
            "[role='article']"
        ]
        
        best_selector = None
        max_count = 0
        
        for selector in potential_selectors:
            try:
                elements = await self.page.query_selector_all(selector)
                count = len(elements)
                
                if count > 0:
                    print(f"  {selector}: {count} 个元素")
                    
                    # 检查元素是否包含房产相关信息
                    if count >= 5 and count <= 50:  # 合理的房产数量范围
                        # 检查第一个元素是否包含价格信息
                        first_element = elements[0]
                        text_content = await first_element.inner_text()
                        
                        if any(keyword in text_content.lower() for keyword in ['€', 'euro', 'prix', 'price', 'm²', 'pièce']):
                            if count > max_count:
                                max_count = count
                                best_selector = selector
                                
            except Exception as e:
                continue
        
        if best_selector:
            self.found_selectors["property_cards"] = best_selector
            print(f"✅ 最佳房产卡片选择器: {best_selector} ({max_count} 个卡片)")
        else:
            print("❌ 未找到合适的房产卡片选择器")
    
    async def analyze_card_structure(self):
        """分析单个卡片的内部结构"""
        print("🔍 正在分析卡片内部结构...")
        
        card_selector = self.found_selectors["property_cards"]
        cards = await self.page.query_selector_all(card_selector)
        
        if not cards:
            return
        
        first_card = cards[0]
        
        # 查找链接
        await self.find_links_in_card(first_card)
        
        # 查找价格
        await self.find_prices_in_card(first_card)
        
        # 查找标题
        await self.find_titles_in_card(first_card)
        
        # 查找图片
        await self.find_images_in_card(first_card)
        
        # 查找位置信息
        await self.find_locations_in_card(first_card)
    
    async def find_links_in_card(self, card):
        """在卡片中查找链接"""
        link_selectors = [
            "a[href*='/ad/']",
            "a[href*='/annonce/']",
            "a[href*='/ventes_immobilieres/']",
            "a[data-test-id*='link']",
            "a[data-testid*='link']",
            "a.ad-link",
            "a.listing-link",
            "a"
        ]
        
        for selector in link_selectors:
            try:
                link = await card.query_selector(selector)
                if link:
                    href = await link.get_attribute("href")
                    if href and ("/ad/" in href or "/annonce/" in href):
                        self.found_selectors["property_link"] = selector
                        print(f"✅ 链接选择器: {selector}")
                        return
            except:
                continue
    
    async def find_prices_in_card(self, card):
        """在卡片中查找价格"""
        price_selectors = [
            "[data-test-id*='price']",
            "[data-testid*='price']",
            ".price",
            ".ad-price",
            ".listing-price",
            "[class*='price']",
            "[class*='Prix']",
            "span:has-text('€')",
            "*:has-text('€')"
        ]
        
        for selector in price_selectors:
            try:
                price_element = await card.query_selector(selector)
                if price_element:
                    text = await price_element.inner_text()
                    if text and '€' in text:
                        self.found_selectors["property_price"] = selector
                        print(f"✅ 价格选择器: {selector}")
                        return
            except:
                continue
    
    async def find_titles_in_card(self, card):
        """在卡片中查找标题"""
        title_selectors = [
            "[data-test-id*='title']",
            "[data-testid*='title']",
            "h1", "h2", "h3", "h4",
            ".title",
            ".ad-title",
            ".listing-title",
            "[class*='title']",
            "[class*='Title']"
        ]
        
        for selector in title_selectors:
            try:
                title_element = await card.query_selector(selector)
                if title_element:
                    text = await title_element.inner_text()
                    if text and len(text.strip()) > 10:  # 标题应该有一定长度
                        self.found_selectors["property_title"] = selector
                        print(f"✅ 标题选择器: {selector}")
                        return
            except:
                continue
    
    async def find_images_in_card(self, card):
        """在卡片中查找图片"""
        image_selectors = [
            "img[data-test-id*='image']",
            "img[data-testid*='image']",
            "img[alt*='photo']",
            "img[alt*='image']",
            ".ad-image img",
            ".listing-image img",
            "img"
        ]
        
        for selector in image_selectors:
            try:
                img_element = await card.query_selector(selector)
                if img_element:
                    src = await img_element.get_attribute("src")
                    if src and ("http" in src or src.startswith("/")):
                        self.found_selectors["property_image"] = selector
                        print(f"✅ 图片选择器: {selector}")
                        return
            except:
                continue
    
    async def find_locations_in_card(self, card):
        """在卡片中查找位置信息"""
        location_selectors = [
            "[data-test-id*='location']",
            "[data-testid*='location']",
            ".location",
            ".ad-location",
            ".listing-location",
            "[class*='location']",
            "[class*='Location']",
            "[class*='ville']",
            "[class*='city']"
        ]
        
        for selector in location_selectors:
            try:
                location_element = await card.query_selector(selector)
                if location_element:
                    text = await location_element.inner_text()
                    if text and len(text.strip()) > 2:
                        self.found_selectors["property_location"] = selector
                        print(f"✅ 位置选择器: {selector}")
                        return
            except:
                continue
    
    async def save_selectors(self):
        """保存找到的选择器"""
        if self.found_selectors:
            with open("found_selectors.json", "w", encoding="utf-8") as f:
                json.dump(self.found_selectors, f, indent=2, ensure_ascii=False)
            
            print("\n" + "="*50)
            print("🎯 找到的选择器:")
            print("="*50)
            for key, value in self.found_selectors.items():
                print(f"{key}: {value}")
            
            print(f"\n✅ 选择器已保存到 found_selectors.json")
            
            # 生成更新的配置代码
            print("\n📝 更新config.py中的SELECTORS:")
            print("-"*30)
            print("SELECTORS = {")
            for key, value in self.found_selectors.items():
                print(f'    "{key}": "{value}",')
            print("}")
        else:
            print("❌ 未找到任何有效选择器")
    
    async def close(self):
        """关闭浏览器"""
        if self.browser:
            await self.browser.close()

async def main():
    """主函数"""
    print("🔍 LeBonCoin智能选择器检测器")
    print("="*50)
    
    finder = SelectorFinder()
    
    try:
        await finder.init_browser()
        await finder.analyze_page_structure()
        await finder.save_selectors()
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
    finally:
        await finder.close()

if __name__ == "__main__":
    asyncio.run(main())
