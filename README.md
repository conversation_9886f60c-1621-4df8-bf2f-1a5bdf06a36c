# LeBonCoin房产爬虫系统

一个现代化的房产爬虫系统，使用前沿的反检测技术爬取LeBonCoin房产信息。

## 🚀 特性

- **反检测技术**: 使用Playwright + Stealth插件绕过反爬机制
- **人类行为模拟**: 随机延迟、鼠标移动、滚动等行为模拟
- **两阶段爬取**:
  - 第一阶段：爬取房产列表基础信息
  - 第二阶段：深入详情页enrichment信息
- **数据导出**: 自动生成CSV文件
- **错误处理**: 完善的日志系统和错误恢复机制
- **可配置**: 灵活的配置系统

## 📋 系统要求

- Python 3.8+
- 足够的内存运行浏览器
- 稳定的网络连接

## 🛠 安装

1. 克隆或下载项目文件
2. 安装依赖：
```bash
pip install -r requirements.txt
```

3. 安装Playwright浏览器：
```bash
playwright install chromium
```

## 📊 获取的数据字段

### 第一阶段（列表页）
- 房源链接
- 房源标题
- 房源价格
- 平米价格（自动计算）
- 面积
- 房产类型（Appartement/Maison/Immeuble等）
- 位置信息
- 主图链接
- 房间数

### 第二阶段（详情页enrichment）
- 详细描述
- 所有图片链接
- 卧室数量
- 建造年份
- 能耗等级
- 楼层信息
- 设施信息（阳台、停车位、电梯等）
- 卖家类型和名称
- 联系信息

## 🎯 使用方法

### 快速开始（推荐）
```bash
# 1. 测试系统是否正常工作
python test_scraper.py

# 2. 运行简化版爬虫（最稳定）
python simple_scraper.py

# 3. 如果需要分析页面结构
python selector_finder.py
```

### 完整版爬虫
```bash
# 第一步：爬取房产列表
python main.py

# 第二步：enrichment详细信息
python detail_scraper.py

# 或者使用一键脚本
python run_scraper.py --stage all
```

### 脚本说明
- `simple_scraper.py` - 简化版爬虫，最稳定，推荐使用
- `test_scraper.py` - 系统测试脚本，验证环境和配置
- `selector_finder.py` - 智能选择器检测，自动分析页面结构
- `main.py` - 完整版爬虫主程序
- `detail_scraper.py` - 详情页enrichment脚本
- `run_scraper.py` - 一键运行脚本

## ⚙️ 配置说明

主要配置在 `config.py` 文件中：

### 搜索配置
```python
SEARCH_URL = "https://www.leboncoin.fr/recherche?category=9&text=NOT%20construire&locations=Palaiseau_91120__48.71303_2.24628_4002&immo_sell_type=old&real_estate_type=1,2&owner_type=all"
```

### 浏览器配置
- `headless`: 是否无头模式（调试时建议设为False）
- `viewport`: 浏览器窗口大小
- `locale`: 语言设置

### 延迟配置
- `page_load`: 页面加载后等待时间
- `between_requests`: 请求间隔
- `scroll`: 滚动延迟
- `click`: 点击延迟

## 📁 输出文件

- `leboncoin_properties.csv`: 基础房产信息
- `leboncoin_properties_detailed.csv`: enrichment后的详细信息
- `logs/scraper.log`: 运行日志
- `property_images/`: 图片下载目录（如果启用）

## 🔧 高级配置

### 修改搜索条件
在 `config.py` 中修改 `SEARCH_URL` 来改变搜索条件：
- `category=9`: 房产类别
- `text=NOT%20construire`: 排除新建房产
- `locations=`: 地理位置
- `immo_sell_type=old`: 二手房
- `real_estate_type=1,2`: 房产类型

### 反检测设置
在 `STEALTH_CONFIG` 中可以调整反检测参数。

### 选择器配置
如果网站结构发生变化，可以在 `SELECTORS` 和 `DETAIL_SELECTORS` 中更新CSS选择器。

## 🚨 注意事项

1. **合法使用**: 请遵守网站的robots.txt和使用条款
2. **频率控制**: 不要过于频繁地请求，避免对服务器造成压力
3. **数据使用**: 爬取的数据仅供个人研究使用
4. **网站变化**: 如果网站结构发生变化，可能需要更新选择器

## 🐛 故障排除

### 常见问题

1. **浏览器启动失败**
   - 确保已安装Playwright浏览器：`playwright install chromium`
   - 检查系统权限

2. **找不到元素**
   - 网站可能更新了结构，需要更新选择器
   - 检查网络连接

3. **被检测为机器人**
   - 增加延迟时间
   - 检查User-Agent设置
   - 考虑使用代理

4. **数据不完整**
   - 检查日志文件了解具体错误
   - 某些字段可能在特定房产中不存在

### 调试模式
设置 `BROWSER_CONFIG["headless"] = False` 可以看到浏览器操作过程，便于调试。

## 📈 性能优化

- 可以通过调整 `DELAYS` 配置来平衡速度和稳定性
- 禁用图片加载可以提高速度
- 使用代理池可以提高并发能力

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

本项目仅供学习和研究使用。
