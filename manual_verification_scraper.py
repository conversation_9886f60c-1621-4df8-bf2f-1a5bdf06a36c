#!/usr/bin/env python3
"""
手动验证版LeBonCoin房产爬虫
在遇到滑块验证时等待用户手动完成
"""

import asyncio
import csv
import json
import re
from datetime import datetime
from playwright.async_api import async_playwright
from playwright_stealth import stealth_async
from fake_useragent import UserAgent

class ManualVerificationScraper:
    def __init__(self):
        self.browser = None
        self.page = None
        self.properties = []
        self.ua = UserAgent()
    
    async def init_browser(self):
        """初始化浏览器"""
        print("🚀 正在启动浏览器...")
        
        playwright = await async_playwright().start()
        
        # 使用基础配置（因为测试显示基础配置可以通过）
        self.browser = await playwright.chromium.launch(
            headless=False,  # 必须显示浏览器以便手动验证
            args=[
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-blink-features=AutomationControlled',
            ]
        )
        
        context = await self.browser.new_context(
            viewport={'width': 1366, 'height': 768},
            user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            locale='fr-FR',
            timezone_id='Europe/Paris',
        )
        
        self.page = await context.new_page()
        await stealth_async(self.page)
        
        print("✅ 浏览器启动成功")
    
    async def navigate_and_wait_for_verification(self, url):
        """导航并等待手动验证"""
        print(f"🌐 正在访问: {url}")
        
        try:
            await self.page.goto(url, wait_until="domcontentloaded", timeout=30000)
            await asyncio.sleep(3)
            
            # 检查是否遇到验证页面
            await self.check_and_wait_for_verification()
            
            return True
            
        except Exception as e:
            print(f"❌ 访问失败: {e}")
            return False
    
    async def check_and_wait_for_verification(self):
        """检查并等待验证完成"""
        max_checks = 60  # 最多检查60次（约5分钟）
        check_count = 0
        
        while check_count < max_checks:
            try:
                content = await self.page.content()
                title = await self.page.title()
                current_url = self.page.url
                
                # 检查是否在验证页面
                verification_indicators = [
                    'please enable js',
                    'disable any ad blocker',
                    'cloudflare',
                    'checking your browser',
                    'ddos protection',
                    'access denied',
                    'captcha',
                    'verification',
                    'challenge',
                    'slider',
                    'slide to verify'
                ]
                
                is_verification_page = any(
                    indicator in content.lower() or indicator in title.lower()
                    for indicator in verification_indicators
                )
                
                if is_verification_page:
                    if check_count == 0:  # 第一次检测到验证页面
                        print("🔒 检测到验证页面！")
                        print("📋 可能的验证类型:")
                        print("   - 滑块验证 (Slide to verify)")
                        print("   - 点击验证 (Click to verify)")
                        print("   - 等待验证 (Waiting for verification)")
                        print("\n⏳ 请在浏览器中手动完成验证...")
                        print("   程序将每5秒检查一次验证状态")
                        print("   验证完成后程序会自动继续")
                        
                        # 保存验证页面截图
                        await self.page.screenshot(path="verification_page.png")
                        print("📸 已保存验证页面截图: verification_page.png")
                    
                    # 显示等待进度
                    dots = "." * (check_count % 4)
                    print(f"\r⏳ 等待验证完成{dots}    ", end="", flush=True)
                    
                    await asyncio.sleep(5)
                    check_count += 1
                    continue
                
                else:
                    # 验证完成或没有验证页面
                    if check_count > 0:
                        print(f"\n✅ 验证完成！继续执行...")
                        await self.page.screenshot(path="after_verification.png")
                        print("📸 已保存验证后页面截图: after_verification.png")
                    
                    # 检查是否成功到达目标页面
                    if 'leboncoin.fr' in current_url and len(content) > 5000:
                        print(f"✅ 成功访问页面: {title}")
                        return True
                    else:
                        print(f"⚠️  页面可能未完全加载，继续等待...")
                        await asyncio.sleep(3)
                        check_count += 1
                        continue
                        
            except Exception as e:
                print(f"❌ 检查验证状态时出错: {e}")
                await asyncio.sleep(5)
                check_count += 1
                continue
        
        print(f"\n⚠️  等待验证超时（{max_checks * 5}秒）")
        return False
    
    async def wait_for_content_load(self):
        """等待页面内容加载"""
        print("⏳ 等待页面内容加载...")
        
        # 等待可能的房产元素出现
        selectors_to_wait = [
            "[data-test-id*='ad']",
            "article",
            ".ad-card",
            ".listing-item",
            "[class*='price']",
            "*:has-text('€')"
        ]
        
        for selector in selectors_to_wait:
            try:
                await self.page.wait_for_selector(selector, timeout=10000)
                print(f"✅ 检测到内容元素: {selector}")
                return True
            except:
                continue
        
        print("⚠️  未检测到预期内容，但继续尝试提取...")
        return False
    
    async def extract_properties(self):
        """提取房产信息"""
        print("🏠 开始提取房产信息...")
        
        # 等待内容加载
        await self.wait_for_content_load()
        await asyncio.sleep(5)
        
        # 保存当前页面用于调试
        await self.page.screenshot(path="extraction_page.png")
        content = await self.page.content()
        with open("extraction_page.html", "w", encoding="utf-8") as f:
            f.write(content)
        print("📸 已保存提取页面截图和HTML")
        
        # 尝试多种提取方法
        extraction_methods = [
            ("data属性", self.extract_by_data_attributes),
            ("类名", self.extract_by_class_names),
            ("文本模式", self.extract_by_text_patterns),
            ("通用选择器", self.extract_by_generic_selectors)
        ]
        
        for method_name, method_func in extraction_methods:
            try:
                print(f"🔍 尝试{method_name}提取...")
                properties = await method_func()
                if properties:
                    self.properties.extend(properties)
                    print(f"✅ {method_name}提取成功，获得 {len(properties)} 个房产")
                    break
                else:
                    print(f"❌ {method_name}提取未找到房产")
            except Exception as e:
                print(f"❌ {method_name}提取失败: {e}")
                continue
        
        if not self.properties:
            print("❌ 所有提取方法都失败")
            await self.manual_analysis()
    
    async def extract_by_data_attributes(self):
        """通过data属性提取"""
        selectors = [
            "[data-test-id*='ad']",
            "[data-testid*='ad']",
            "[data-qa*='ad']"
        ]
        
        for selector in selectors:
            elements = await self.page.query_selector_all(selector)
            if len(elements) >= 3:
                print(f"找到 {len(elements)} 个元素 (选择器: {selector})")
                return await self.process_elements(elements)
        return []
    
    async def extract_by_class_names(self):
        """通过类名提取"""
        selectors = [
            ".ad-card",
            ".adcard", 
            ".listing-item",
            ".search-result",
            "[class*='ad-']",
            "[class*='listing']"
        ]
        
        for selector in selectors:
            elements = await self.page.query_selector_all(selector)
            if len(elements) >= 3:
                print(f"找到 {len(elements)} 个元素 (选择器: {selector})")
                return await self.process_elements(elements)
        return []
    
    async def extract_by_text_patterns(self):
        """通过文本模式提取"""
        # 查找包含价格的元素
        price_elements = await self.page.query_selector_all("*:has-text('€')")
        print(f"找到 {len(price_elements)} 个包含价格的元素")
        
        if len(price_elements) >= 5:
            # 获取这些元素的父容器
            containers = []
            for element in price_elements[:20]:
                try:
                    # 向上查找可能的容器
                    for level in range(1, 4):
                        parent = element
                        for _ in range(level):
                            parent_elem = await parent.query_selector('xpath=..')
                            if parent_elem:
                                parent = parent_elem
                            else:
                                break
                        
                        if parent:
                            parent_text = await parent.inner_text()
                            if len(parent_text) > 50 and '€' in parent_text:
                                containers.append(parent)
                                break
                except:
                    continue
            
            if containers:
                print(f"找到 {len(containers)} 个价格容器")
                return await self.process_elements(containers[:15])
        
        return []
    
    async def extract_by_generic_selectors(self):
        """通过通用选择器提取"""
        selectors = [
            "article",
            ".item",
            "[role='article']",
            "li"
        ]
        
        for selector in selectors:
            elements = await self.page.query_selector_all(selector)
            
            # 过滤出可能包含房产信息的元素
            valid_elements = []
            for element in elements:
                try:
                    text = await element.inner_text()
                    if len(text) > 30 and '€' in text:
                        valid_elements.append(element)
                except:
                    continue
            
            if len(valid_elements) >= 3:
                print(f"找到 {len(valid_elements)} 个有效元素 (选择器: {selector})")
                return await self.process_elements(valid_elements[:15])
        
        return []
    
    async def process_elements(self, elements):
        """处理元素列表"""
        properties = []
        
        for i, element in enumerate(elements):
            try:
                property_data = await self.extract_single_property(element, i)
                if property_data:
                    properties.append(property_data)
                    print(f"  ✅ 房产 {i+1}: {property_data.get('title', 'Unknown')[:40]}...")
            except Exception as e:
                print(f"  ❌ 房产 {i+1} 提取失败: {e}")
                continue
        
        return properties
    
    async def extract_single_property(self, element, index):
        """提取单个房产信息"""
        property_data = {
            'index': index + 1,
            'extracted_at': datetime.now().isoformat()
        }
        
        try:
            # 获取元素的所有文本内容
            full_text = await element.inner_text()
            
            # 提取价格
            price = self.extract_price_from_text(full_text)
            if price:
                property_data['price'] = price
            
            # 提取面积
            area = self.extract_area_from_text(full_text)
            if area:
                property_data['area'] = area
            
            # 提取房间数
            rooms = self.extract_rooms_from_text(full_text)
            if rooms:
                property_data['rooms'] = rooms
            
            # 提取位置
            location = self.extract_location_from_text(full_text)
            if location:
                property_data['location'] = location
            
            # 提取标题
            lines = [line.strip() for line in full_text.split('\n') if line.strip()]
            if lines:
                property_data['title'] = lines[0][:100]
            
            # 提取链接
            link = await self.extract_link_from_element(element)
            if link:
                property_data['url'] = link
            
            # 提取图片
            image = await self.extract_image_from_element(element)
            if image:
                property_data['image'] = image
            
            # 计算平米价格
            if price and area and area > 0:
                property_data['price_per_sqm'] = round(price / area, 2)
            
            # 只返回包含关键信息的房产
            if price or property_data.get('title'):
                return property_data
            
        except Exception as e:
            print(f"❌ 提取房产 {index+1} 时出错: {e}")
        
        return None
    
    def extract_price_from_text(self, text):
        """从文本中提取价格"""
        patterns = [
            r'(\d{1,3}(?:\s\d{3})*)\s*€',
            r'(\d+(?:\.\d{3})*)\s*€',
            r'€\s*(\d{1,3}(?:\s\d{3})*)',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                try:
                    price_str = match.replace(' ', '').replace('.', '')
                    price = int(price_str)
                    if 10000 <= price <= 10000000:
                        return price
                except:
                    continue
        return None
    
    def extract_area_from_text(self, text):
        """从文本中提取面积"""
        patterns = [
            r'(\d+(?:[.,]\d+)?)\s*m[²2]',
            r'(\d+)\s*m[²2]'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                try:
                    area = float(match.replace(',', '.'))
                    if 10 <= area <= 1000:
                        return area
                except:
                    continue
        return None
    
    def extract_rooms_from_text(self, text):
        """从文本中提取房间数"""
        patterns = [
            r'(\d+)\s*pièces?',
            r'(\d+)\s*p\.',
            r'T(\d+)',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                try:
                    rooms = int(match)
                    if 1 <= rooms <= 20:
                        return rooms
                except:
                    continue
        return None
    
    def extract_location_from_text(self, text):
        """从文本中提取位置"""
        patterns = [
            r'(\d{5})\s+([A-Za-zÀ-ÿ\s-]+)',
            r'([A-Za-zÀ-ÿ\s-]+)\s+\((\d{2,3})\)',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            if matches:
                return ' '.join(matches[0]).strip()
        return None
    
    async def extract_link_from_element(self, element):
        """从元素中提取链接"""
        try:
            link_element = await element.query_selector("a")
            if link_element:
                href = await link_element.get_attribute('href')
                if href:
                    if href.startswith('/'):
                        href = 'https://www.leboncoin.fr' + href
                    return href
        except:
            pass
        return None
    
    async def extract_image_from_element(self, element):
        """从元素中提取图片"""
        try:
            img_element = await element.query_selector("img")
            if img_element:
                src = await img_element.get_attribute('src')
                if src and ('http' in src or src.startswith('/')):
                    if src.startswith('/'):
                        src = 'https://www.leboncoin.fr' + src
                    return src
        except:
            pass
        return None
    
    async def manual_analysis(self):
        """手动分析页面"""
        print("\n🔍 进行手动页面分析...")
        print("请在浏览器中查看页面，程序将等待30秒")
        print("你可以:")
        print("1. 检查页面是否正确加载")
        print("2. 查看是否有房产信息显示")
        print("3. 手动滚动页面加载更多内容")
        
        await asyncio.sleep(30)
        
        # 再次尝试提取
        print("🔄 30秒后重新尝试提取...")
        await self.extract_properties()
    
    async def save_results(self):
        """保存结果"""
        if not self.properties:
            print("❌ 没有数据可保存")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_filename = f"leboncoin_manual_{timestamp}.csv"
        json_filename = f"leboncoin_manual_{timestamp}.json"
        
        # 保存CSV
        fieldnames = set()
        for prop in self.properties:
            fieldnames.update(prop.keys())
        fieldnames = sorted(list(fieldnames))
        
        with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(self.properties)
        
        print(f"✅ CSV数据已保存: {csv_filename}")
        
        # 保存JSON
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(self.properties, f, indent=2, ensure_ascii=False)
        
        print(f"✅ JSON数据已保存: {json_filename}")
        print(f"📊 总共获取 {len(self.properties)} 个房产信息")
        
        # 显示前3个房产的信息
        for i, prop in enumerate(self.properties[:3]):
            print(f"\n房产 {i+1}:")
            for key, value in prop.items():
                if key not in ['extracted_at', 'index']:
                    print(f"  {key}: {value}")
    
    async def close(self):
        """关闭浏览器"""
        if self.browser:
            await self.browser.close()

async def main():
    """主函数"""
    search_url = "https://www.leboncoin.fr/recherche?category=9&text=NOT%20construire&locations=Palaiseau_91120__48.71303_2.24628_4002&immo_sell_type=old&real_estate_type=1,2&owner_type=all"
    
    print("🏠 LeBonCoin手动验证版房产爬虫")
    print("支持滑块验证等手动操作")
    print("="*50)
    
    scraper = ManualVerificationScraper()
    
    try:
        await scraper.init_browser()
        
        if await scraper.navigate_and_wait_for_verification(search_url):
            await scraper.extract_properties()
            await scraper.save_results()
        else:
            print("❌ 无法完成验证或访问页面")
    
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
    
    finally:
        print("\n🔍 程序即将结束，浏览器将保持打开10秒供检查...")
        await asyncio.sleep(10)
        await scraper.close()

if __name__ == "__main__":
    asyncio.run(main())
