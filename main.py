"""
LeBonCoin房产爬虫 - 主程序
使用Playwright + Stealth技术绕过反爬机制
"""

import asyncio
import json
import os
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin

from playwright.async_api import async_playwright, <PERSON>, Browser
from playwright_stealth import stealth_async

from config import *
from utils import *

class LeBonCoinScraper:
    def __init__(self):
        self.logger = setup_logging(OUTPUT_CONFIG["logs_dir"])
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None
        self.properties_data: List[Dict[str, Any]] = []
        
        # 创建必要目录
        create_directories([
            OUTPUT_CONFIG["images_dir"],
            OUTPUT_CONFIG["logs_dir"]
        ])
    
    async def init_browser(self) -> None:
        """初始化浏览器"""
        self.logger.info("正在初始化浏览器...")
        
        playwright = await async_playwright().start()
        
        # 启动浏览器
        self.browser = await playwright.chromium.launch(
            headless=BROWSER_CONFIG["headless"],
            args=[
                '--no-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-dev-shm-usage',
                '--disable-extensions',
                '--disable-plugins',
                '--disable-images',  # 禁用图片加载以提高速度
                '--disable-javascript',  # 可选：禁用JS以提高速度
            ]
        )
        
        # 创建页面
        context = await self.browser.new_context(
            viewport=BROWSER_CONFIG["viewport"],
            user_agent=get_random_user_agent(),
            locale=BROWSER_CONFIG["locale"],
            timezone_id=BROWSER_CONFIG["timezone_id"]
        )
        
        self.page = await context.new_page()
        
        # 应用stealth插件
        await stealth_async(self.page)
        
        self.logger.info("浏览器初始化完成")
    
    async def navigate_to_search(self) -> bool:
        """导航到搜索页面"""
        try:
            self.logger.info(f"正在访问搜索页面: {SEARCH_URL}")
            
            await self.page.goto(SEARCH_URL, wait_until="networkidle")
            await random_delay(*DELAYS["page_load"])
            
            # 模拟人类行为
            await simulate_human_behavior(self.page)
            
            # 检查是否成功加载
            title = await self.page.title()
            self.logger.info(f"页面标题: {title}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"导航到搜索页面失败: {e}")
            return False
    
    async def extract_property_cards(self) -> List[Dict[str, Any]]:
        """提取当前页面的房产卡片信息"""
        properties = []
        
        try:
            # 等待房产卡片加载
            await self.page.wait_for_selector(SELECTORS["property_cards"], timeout=10000)
            
            # 获取所有房产卡片
            cards = await self.page.query_selector_all(SELECTORS["property_cards"])
            self.logger.info(f"找到 {len(cards)} 个房产卡片")
            
            for i, card in enumerate(cards):
                try:
                    property_data = await self.extract_single_property(card, i)
                    if property_data:
                        properties.append(property_data)
                        
                    # 随机延迟避免被检测
                    await random_delay(0.1, 0.3)
                    
                except Exception as e:
                    self.logger.warning(f"提取第 {i+1} 个房产信息失败: {e}")
                    continue
            
            self.logger.info(f"成功提取 {len(properties)} 个房产信息")
            return properties
            
        except Exception as e:
            self.logger.error(f"提取房产卡片失败: {e}")
            return []
    
    async def extract_single_property(self, card, index: int) -> Optional[Dict[str, Any]]:
        """提取单个房产的信息"""
        try:
            property_data = {}
            
            # 提取链接
            link_element = await card.query_selector(SELECTORS["property_link"])
            if link_element:
                href = await link_element.get_attribute("href")
                property_data["url"] = normalize_url(href, BASE_URL)
            else:
                self.logger.warning(f"房产 {index+1}: 未找到链接")
                return None
            
            # 提取标题
            title_element = await card.query_selector(SELECTORS["property_title"])
            if title_element:
                property_data["title"] = await title_element.inner_text()
            
            # 提取价格
            price_element = await card.query_selector(SELECTORS["property_price"])
            if price_element:
                price_text = await price_element.inner_text()
                property_data["price"] = clean_price(price_text)
                property_data["price_text"] = price_text
            
            # 提取主图
            image_element = await card.query_selector(SELECTORS["property_image"])
            if image_element:
                src = await image_element.get_attribute("src")
                property_data["main_image"] = normalize_url(src, BASE_URL) if src else None
            
            # 提取位置
            location_element = await card.query_selector(SELECTORS["property_location"])
            if location_element:
                property_data["location"] = await location_element.inner_text()
            
            # 提取属性信息（面积、房间数等）
            attributes_element = await card.query_selector(SELECTORS["property_attributes"])
            if attributes_element:
                attributes_text = await attributes_element.inner_text()
                property_data["attributes"] = attributes_text
                
                # 解析面积
                area = extract_area(attributes_text)
                property_data["area"] = area
                
                # 解析房间数
                rooms = extract_rooms(attributes_text)
                property_data["rooms"] = rooms
                
                # 计算平米价格
                if property_data.get("price") and area:
                    property_data["price_per_sqm"] = calculate_price_per_sqm(
                        property_data["price"], area
                    )
                
                # 提取房产类型
                property_data["property_type"] = extract_property_type(attributes_text)
            
            self.logger.debug(f"房产 {index+1}: {property_data.get('title', 'Unknown')}")
            return property_data
            
        except Exception as e:
            self.logger.error(f"提取房产 {index+1} 信息时出错: {e}")
            return None
    
    async def scrape_listing_page(self) -> List[Dict[str, Any]]:
        """爬取列表页面"""
        all_properties = []
        page_num = 1
        
        while True:
            self.logger.info(f"正在爬取第 {page_num} 页...")
            
            # 提取当前页面的房产信息
            properties = await self.extract_property_cards()
            all_properties.extend(properties)
            
            # 检查是否有下一页
            has_next = await self.check_and_go_next_page()
            if not has_next:
                self.logger.info("没有更多页面，爬取完成")
                break
            
            page_num += 1
            
            # 限制页面数量避免过度爬取
            if page_num > 10:  # 最多爬取10页
                self.logger.info("达到最大页面限制，停止爬取")
                break
        
        return all_properties
    
    async def check_and_go_next_page(self) -> bool:
        """检查并跳转到下一页"""
        try:
            # 尝试找到下一页按钮
            next_button = await self.page.query_selector(SELECTORS["next_page"])
            
            if next_button:
                # 检查按钮是否可点击
                is_disabled = await next_button.get_attribute("disabled")
                if not is_disabled:
                    await next_button.click()
                    await random_delay(*DELAYS["page_load"])
                    return True
            
            # 尝试找到"加载更多"按钮
            load_more = await self.page.query_selector(SELECTORS["load_more"])
            if load_more:
                await load_more.click()
                await random_delay(*DELAYS["page_load"])
                return True
            
            return False
            
        except Exception as e:
            self.logger.warning(f"跳转下一页失败: {e}")
            return False
    
    async def close(self) -> None:
        """关闭浏览器"""
        if self.browser:
            await self.browser.close()
            self.logger.info("浏览器已关闭")

async def main():
    """主函数"""
    scraper = LeBonCoinScraper()
    
    try:
        # 初始化浏览器
        await scraper.init_browser()
        
        # 导航到搜索页面
        if not await scraper.navigate_to_search():
            return
        
        # 爬取列表页面
        properties = await scraper.scrape_listing_page()
        
        # 保存数据
        if properties:
            save_to_csv(properties, OUTPUT_CONFIG["csv_filename"])
            scraper.logger.info(f"第一阶段完成，共获取 {len(properties)} 个房产信息")
        else:
            scraper.logger.warning("未获取到任何房产信息")
    
    except Exception as e:
        scraper.logger.error(f"爬取过程中出现错误: {e}")
    
    finally:
        await scraper.close()

if __name__ == "__main__":
    asyncio.run(main())
