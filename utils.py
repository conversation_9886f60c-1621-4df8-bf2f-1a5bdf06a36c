"""
工具函数 - 房产爬虫系统
"""

import asyncio
import random
import re
import os
import logging
from typing import Optional, Dict, Any, List
from urllib.parse import urljoin, urlparse
import pandas as pd
from fake_useragent import UserAgent

def setup_logging(log_dir: str = "logs") -> logging.Logger:
    """设置日志系统"""
    os.makedirs(log_dir, exist_ok=True)
    
    logger = logging.getLogger("leboncoin_scraper")
    logger.setLevel(logging.INFO)
    
    # 文件处理器
    file_handler = logging.FileHandler(
        os.path.join(log_dir, "scraper.log"),
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

def get_random_user_agent() -> str:
    """获取随机User-Agent"""
    ua = UserAgent()
    return ua.chrome

async def random_delay(min_delay: float, max_delay: float) -> None:
    """随机延迟"""
    delay = random.uniform(min_delay, max_delay)
    await asyncio.sleep(delay)

def clean_price(price_text: str) -> Optional[float]:
    """清理价格文本，提取数字"""
    if not price_text:
        return None
    
    # 移除所有非数字字符，保留小数点
    price_clean = re.sub(r'[^\d.]', '', price_text.replace(',', '.'))
    
    try:
        return float(price_clean)
    except ValueError:
        return None

def extract_area(text: str) -> Optional[float]:
    """从文本中提取面积信息"""
    if not text:
        return None
    
    # 查找面积模式：数字 + m² 或 m2
    area_pattern = r'(\d+(?:[.,]\d+)?)\s*m[²2]'
    match = re.search(area_pattern, text, re.IGNORECASE)
    
    if match:
        area_str = match.group(1).replace(',', '.')
        try:
            return float(area_str)
        except ValueError:
            return None
    
    return None

def extract_rooms(text: str) -> Optional[int]:
    """从文本中提取房间数"""
    if not text:
        return None
    
    # 查找房间数模式
    rooms_patterns = [
        r'(\d+)\s*pièces?',
        r'(\d+)\s*p\.',
        r'T(\d+)',
        r'(\d+)\s*rooms?'
    ]
    
    for pattern in rooms_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            try:
                return int(match.group(1))
            except ValueError:
                continue
    
    return None

def calculate_price_per_sqm(price: Optional[float], area: Optional[float]) -> Optional[float]:
    """计算平米价格"""
    if price and area and area > 0:
        return round(price / area, 2)
    return None

def normalize_url(url: str, base_url: str) -> str:
    """标准化URL"""
    if url.startswith('http'):
        return url
    return urljoin(base_url, url)

def extract_property_type(text: str) -> str:
    """提取房产类型"""
    if not text:
        return "Unknown"
    
    text_lower = text.lower()
    
    if 'appartement' in text_lower:
        return 'Appartement'
    elif 'maison' in text_lower:
        return 'Maison'
    elif 'immeuble' in text_lower:
        return 'Immeuble'
    elif 'studio' in text_lower:
        return 'Studio'
    elif 'loft' in text_lower:
        return 'Loft'
    elif 'duplex' in text_lower:
        return 'Duplex'
    else:
        return 'Autre'

def save_to_csv(data: List[Dict[str, Any]], filename: str) -> None:
    """保存数据到CSV文件"""
    if not data:
        print(f"没有数据可保存到 {filename}")
        return
    
    df = pd.DataFrame(data)
    df.to_csv(filename, index=False, encoding='utf-8-sig')
    print(f"数据已保存到 {filename}，共 {len(data)} 条记录")

def create_directories(dirs: List[str]) -> None:
    """创建必要的目录"""
    for dir_path in dirs:
        os.makedirs(dir_path, exist_ok=True)

async def simulate_human_behavior(page) -> None:
    """模拟人类浏览行为"""
    # 随机滚动
    scroll_count = random.randint(1, 3)
    for _ in range(scroll_count):
        await page.evaluate("window.scrollBy(0, Math.random() * 500 + 200)")
        await random_delay(0.5, 1.0)
    
    # 随机鼠标移动
    viewport = await page.viewport_size()
    if viewport:
        x = random.randint(100, viewport['width'] - 100)
        y = random.randint(100, viewport['height'] - 100)
        await page.mouse.move(x, y)
        await random_delay(0.2, 0.5)

def is_valid_property_url(url: str) -> bool:
    """验证是否为有效的房产URL"""
    if not url:
        return False
    
    # 检查URL格式
    parsed = urlparse(url)
    if not parsed.netloc:
        return False
    
    # 检查是否包含房产相关路径
    property_indicators = ['/ventes_immobilieres/', '/ad/', '/annonce/']
    return any(indicator in url for indicator in property_indicators)
