import re
import time
import os
import random
import json
import datetime
import threading
from bs4 import BeautifulSoup
import undetected_chromedriver as uc
from login import is_logged_in, login_to_site

def setup_driver():
    options = uc.ChromeOptions()
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_argument("--window-size=1200,900")
    driver = uc.Chrome(options=options)
    return driver

def extract_product_details(driver, product_url):
    product_data = {
        "Product_URL": product_url,
        "Category1": None,
        "Category2": None,
        "Category3": None,
        "Brand": None,
        "Brand_Link": None,
        "Wholesaler_Name": None,
        "Wholesaler_Link": None,
        "Minimum_Order": None,
        "Description": None,
        "Size_and_Fit": None,
        "Product_Info": None,
        "Supplier_Info": None,
        "Product_List": []
    }
    
    try:
        driver.get(product_url)
        time.sleep(2 + random.random())  # Random wait between 2-3 seconds

        # Scroll to ensure all elements are loaded
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight/2);")
        time.sleep(1)
        
        soup = BeautifulSoup(driver.page_source, 'html.parser')
        
  # ===== COLOR IMAGE MAPPING =====
        color_images = {}
        gallery = soup.find('div', id='vertical-gallery')
        if gallery:
            default_color = gallery.get('data-default-color', '').replace('_',' ').strip().upper() or 'DEFAULT'
            for img in gallery.find_all('img', class_='preview'):
                color_name = img.get('data-color', '').replace('_',' ').strip().upper() or default_color
                image_url = img.get('data-src') or img.get('src')
                if image_url and image_url.startswith(('http://', 'https://')):
                    clean_url = image_url.split('?image_process=')[0]
                    color_images.setdefault(color_name, []).append(clean_url)
     
        # ===== PRODUCT METADATA =====
        # Categories
        category_links = soup.find_all('a', class_='title-cat')
        for i, link in enumerate(category_links[:3], 1):
            product_data[f"Category{i}"] = link.get_text(strip=True)
        
        # Brand
        brand_element = soup.find('h2', id='brand')
        if brand_element:
            product_data["Brand"] = brand_element.get_text(strip=True)
        
        # ===== BRAND LINK =====
        brand_cont = soup.find('a', id='brand-cont')
        if brand_cont:
            product_data["Brand_Link"] = brand_cont.get('href', '').strip()
            # Ensure we have the full URL
            if product_data["Brand_Link"] and not product_data["Brand_Link"].startswith('http'):
                product_data["Brand_Link"] = f"https://parisfashionshops.com{product_data['Brand_Link']}"

        # ===== WHOLESALER INFO =====
        wholesaler_link = soup.find('a', id='link-wholesaler')
        if wholesaler_link:
            product_data["Wholesaler_Name"] = wholesaler_link.get_text(strip=True)
            product_data["Wholesaler_Link"] = wholesaler_link.get('href', '').strip()
            # Ensure full URL for wholesaler link too
            if product_data["Wholesaler_Link"] and not product_data["Wholesaler_Link"].startswith('http'):
                product_data["Wholesaler_Link"] = f"https://parisfashionshops.com{product_data['Wholesaler_Link']}"

        # Minimum Order
        minimum_div = soup.find('div', id='minimum-txt')
        if minimum_div:
            price_span = minimum_div.find('span', string=lambda t: t and '€' in t)
            if price_span:
                product_data["Minimum_Order"] = price_span.get_text(strip=True) + " excl tax"

        # Description
        description = soup.find('p', id='description')
        if description:
            desc_text = description.get_text(separator='\n')
            lines = [line.strip() for line in desc_text.split('\n') if line.strip()]
            product_data["Description"] = '\n'.join(lines)

        # ===== SIZE AND FIT =====
        size_fit_section = soup.find('h3', class_='title-desc', string='Taille et coupe')
        if size_fit_section:
            size_fit_div = size_fit_section.find_parent('div', class_='content-desc')
            if size_fit_div:
                size_fit_items = []
                for item in size_fit_div.find_all('p', class_='caract-prod'):
                    # Remove the title-inside span and keep the rest
                    title = item.find('span', class_='title-inside')
                    if title:
                        title_text = title.get_text(strip=True)
                        title.extract()  # Remove the title span
                    item_text = item.get_text(' ', strip=True)
                    size_fit_items.append(f"{title_text}{item_text}")
                product_data["Size_and_Fit"] = '\n'.join(size_fit_items)
        

        # ===== PRODUCT INFO =====
        product_info_section = soup.find('h3', class_='title-desc', string='Infos produit')
        if product_info_section:
            product_info_div = product_info_section.find_parent('div', class_='content-desc')
            if product_info_div:
                product_info_items = []
                for item in product_info_div.find_all('p', class_='caract-prod'):
                    title = item.find('span', class_='title-inside')
                    if title:
                        title_text = title.get_text(strip=True)
                        title.extract()
                    item_text = item.get_text(' ', strip=True)
                    product_info_items.append(f"{title_text}{item_text}")
                product_data["Product_Info"] = '\n'.join(product_info_items)

        
        # ===== SUPPLIER INFO =====
        supplier_section = soup.find('h3', class_='title-desc', string='Infos fournisseur')
        if supplier_section:
            supplier_div = supplier_section.find_parent('div', class_='content-desc')
            if supplier_div:
                supplier_items = []
                for item in supplier_div.find_all('p', class_='caract-prod'):
                    # Handle both span titles and direct text
                    title = item.find('span', class_='title-inside')
                    if title:
                        title_text = title.get_text(strip=True)
                        title.extract()
                    else:
                        title_text = ""
                    item_text = item.get_text(' ', strip=True)
                    supplier_items.append(f"{title_text}{item_text}")
                product_data["Supplier_Info"] = '\n'.join(supplier_items)

        
        # ===== PRODUCT VARIANTS =====
        product_rows = soup.find_all('tr', class_='row value')
        for row in product_rows:
            variant = {
                "Color": None,
                "Size_Quantity": {},
                "Images": [],
                "Price_excl_tax": None,
                "Unit_Price": None,
                "Total_Quantity": None,
                "Stock_Status": "In Stock"
            }

           
            # Color and Images
            color_div = row.find('div', class_='color')
            if color_div and 'data-color' in color_div.attrs:
                color_name = color_div['data-color'].upper()
                variant["Color"] = color_name.replace('_',' ')
                variant["Images"] = color_images.get(color_name.replace('_',' '), [])
                # print(color_images)
            # Size and Quantity
            size_qty_pairs = []
            for div in row.find_all('div', class_='size_nb_col'):
                size = div.find('span', class_='size')
                qty = div.find('span', class_='qty')
                if size and qty:
                    size_qty_pairs.append(f"{size.get_text(strip=True)}:{qty.get_text(strip=True)}")
            variant["Size_Quantity"] = "; ".join(size_qty_pairs) + ";"

            # Prices
            price_td = row.find('td', class_='price item')
            if price_td:
                if price_excl := price_td.find('span', class_='finale-price'):
                    variant["Price_excl_tax"] = price_excl.get_text(strip=True)
                if mobile_price := price_td.find('div', class_='label-mobile'):
                    variant["Unit_Price"] = mobile_price.get_text(strip=True)

            # Extract Total Quantity
            # Extract Total Quantity - check both possible locations
            sizes_nb_div = row.find('td', class_='sizes_nb item')
            if sizes_nb_div:
                # First try to find the summary quantity in the details row
                details_row = row.find_next_sibling('tr', class_='row details')
                if details_row:
                    details_label = details_row.find('td', class_='sizes_nb item').find('div', class_='label')
                    if details_label and details_label.find('span'):
                        variant["Total_Quantity"] = details_label.span.get_text(strip=True)
                else:
                    # Fall back to individual size quantities if no details row
                    qty_spans = sizes_nb_div.find_all('span', class_='qty')
                    if qty_spans:
                        total_qty = sum(int(qty.get_text(strip=True)) for qty in qty_spans)
                        variant["Total_Quantity"] = f"{total_qty} Pièces"

            # Stock Status
            alert_div = row.find('div', class_='alert-item')
            if alert_div and "En rupture de stock" in alert_div.get_text(strip=True):
                variant["Stock_Status"] = "En rupture de stock"
            else:
                variant["Stock_Status"] = "In Stock"

            product_data["Product_List"].append(variant)
        
        return product_data
        
    except Exception as e:
        print(f"Error extracting from {product_url}: {e}")
        return None

def save_product_details(product_data, filename):
    try:
        existing_data = []
        if os.path.exists(filename):
            with open(filename, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
        existing_data.append(product_data)
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(existing_data, f, indent=2, ensure_ascii=False)
    except Exception as e:
        print(f"Error saving product details: {e}")

def process_batch(driver, input_file, output_file, start_line, end_line):
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    final_output = f"{output_file}_{start_line}-{end_line}.json"
    
    # First attempt to login
    if not is_logged_in(driver):
        print(f"Attempting login for batch {start_line}-{end_line}")
        login_success = login_to_site(driver)
        
        if not login_success:
            print(f"❌ Failed to login for batch {start_line}-{end_line}")
            driver.quit()
            return
        print(f"✅ Login successful for batch {start_line}-{end_line}")
    
    try:
        # Load already processed URLs from output file if exists
        processed_urls = set()
        if os.path.exists(final_output):
            with open(final_output, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
                processed_urls = {item["Product_URL"] for item in existing_data}
        
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()[1:][start_line:end_line]

        for i, line in enumerate(lines, start_line + 1):
            try:
                _, product_url = line.strip().split('|')
                
                # Skip if already processed
                if product_url in processed_urls:
                    print(f"⏩ Already processed: {product_url} (Skipping)")
                    continue
                
                # Check login status periodically
                if i % 20 == 0 and not is_logged_in(driver):
                    print(f"⚠️ Session expired during processing for batch {start_line}-{end_line}. Attempting re-login...")
                    if login_to_site(driver):
                        print(f"✅ Successfully re-logged in for batch {start_line}-{end_line}")
                    else:
                        print(f"❌ Could not re-login for batch {start_line}-{end_line}")
                        break
                
                print(f"\nProcessing product {i}: {product_url}")
                if product_data := extract_product_details(driver, product_url):
                    save_product_details(product_data, final_output)
                    processed_urls.add(product_url)  # Add to processed set
                time.sleep(random.uniform(2, 3))
            except Exception as e:
                print(f"Error processing line {i}: {e}")
    finally:
        driver.quit()

def main():
    print("🚀 Starting parallel product detail extraction")
    start_time = time.time()
    
    config = {
        "input_file": "tocrawl/women_product_links_1-500.txt",
        "output_prefix": "crawled/detail_women_product_1-500",
        "batches": [
            {"start": 0, "end": 10000},
            {"start": 10001, "end": 20000},
            {"start": 20001, "end": 30000},
            {"start": 30001, "end": 40000},
            {"start": 40001, "end": 50000},
            {"start": 50001, "end": 60000},
        ]
    }

    threads = []
    for batch in config["batches"]:
        driver = setup_driver()
        t = threading.Thread(
            target=process_batch,
            args=(driver, config["input_file"], config["output_prefix"], batch["start"], batch["end"])
        )
        t.start()
        threads.append(t)
        time.sleep(10)  # Increased stagger time between thread starts

    for t in threads:
        t.join()

    print(f"\n⏱️ Total processing time: {(time.time() - start_time)/60:.2f} minutes")
    print("✅ All parallel sessions completed")

if __name__ == "__main__":
    main()